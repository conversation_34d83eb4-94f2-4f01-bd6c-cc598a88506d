# HelloFIDO Plugin Deployment Guide

## Quick Start

### For Testing (Recommended)
```batch
# Build and test in one step
test.bat
```

### For Development
```batch
# Build only
build.bat

# Copy dependencies if needed
copy-deps.bat

# Verify installation
verify-plugin.bat
```

## File Structure

After successful deployment, your KeePass plugins folder should contain:

```
KeePass-Portable/Plugins/
├── HelloFIDO.dll                                    # Main plugin
├── Fido2.dll                                       # FIDO2 core library
├── Fido2.Models.dll                                # FIDO2 models
├── CBOR.dll                                        # CBOR encoding
├── NSec.Cryptography.dll                           # Cryptography
├── Newtonsoft.Json.dll                             # JSON handling
├── Microsoft.IdentityModel.JsonWebTokens.dll       # JWT tokens
├── Microsoft.IdentityModel.Logging.dll             # Identity logging
├── Microsoft.IdentityModel.Tokens.dll              # Identity tokens
├── System.IdentityModel.Tokens.Jwt.dll             # System JWT
├── System.Memory.dll                               # Memory management
├── System.Buffers.dll                              # Buffer management
├── System.Runtime.CompilerServices.Unsafe.dll      # Unsafe operations
├── System.Numerics.Vectors.dll                     # Vector operations
├── System.Security.Cryptography.Cng.dll            # Windows CNG
├── Numbers.dll                                     # Number utilities
└── URIUtility.dll                                  # URI utilities
```

## Verification Steps

1. **Check File Presence**:
   ```batch
   verify-plugin.bat
   ```

2. **Start KeePass**:
   ```batch
   cd KeePass-Portable
   KeePass.exe
   ```

3. **Verify Plugin Loading**:
   - Check Tools menu for "HelloFIDO Settings"
   - Look for "HelloFIDO (Windows Hello + FIDO2)" in key provider dropdown

## Common Issues and Solutions

### Missing Dependencies
**Symptom**: Plugin fails to load with assembly errors
**Solution**: Run `copy-deps.bat` to copy all required dependencies

### File Locked Errors
**Symptom**: Cannot copy files because they're in use
**Solution**: Close KeePass completely before copying files
```batch
taskkill /f /im KeePass.exe
copy-deps.bat
```

### Plugin Not Visible
**Symptom**: Plugin loads but doesn't appear in menus
**Solution**: Check that HelloFIDO.dll is in the plugins folder and restart KeePass

## Build Scripts Reference

- **`build.bat`**: Complete build with dependency copying
- **`test.bat`**: Build, copy, and start KeePass for testing
- **`clean.bat`**: Clean all build artifacts
- **`copy-deps.bat`**: Copy only the dependencies
- **`verify-plugin.bat`**: Verify installation completeness

## Production Deployment

For production use:

1. Build release version:
   ```batch
   msbuild HelloFIDO.sln /p:Configuration=Release
   ```

2. Copy from `bin\Release\` instead of `bin\Debug\`

3. Test thoroughly with your specific KeePass setup

4. Consider creating an installer package for easier distribution

## Security Considerations

- All dependencies are from trusted NuGet packages
- Plugin uses Windows security infrastructure
- No sensitive data is stored in plain text
- Keys are generated deterministically based on authentication

## Support

If you encounter issues:
1. Run `verify-plugin.bat` to check installation
2. Check KeePass error logs
3. Ensure Windows Hello is properly configured
4. Test with a simple database first
