{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\HelloFIDO\\HelloFIDO.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\HelloFIDO\\HelloFIDO.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\HelloFIDO\\HelloFIDO.csproj", "projectName": "HelloFIDO", "projectPath": "C:\\Users\\<USER>\\source\\repos\\HelloFIDO\\HelloFIDO.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\HelloFIDO\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net481"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net481": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net481": {"dependencies": {"Fido2": {"target": "Package", "version": "[2.0.2, )"}, "Microsoft.Windows.SDK.Contracts": {"target": "Package", "version": "[10.0.22621.2428, )"}}}}, "runtimes": {"win": {"#import": []}}}}}