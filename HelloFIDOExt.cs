using System;
using System.Windows.Forms;
using KeePass.Plugins;
using KeePassLib.Keys;

namespace HelloFIDO
{
    /// <summary>
    /// Main plugin class for HelloFIDO - implements Windows Hello and FIDO2/WebAuthn authentication for KeePass
    /// </summary>
    public sealed class HelloFIDOExt : Plugin
    {
        private IPluginHost m_host = null;
        private HelloFIDOKeyProvider m_keyProvider = null;
        private ToolStripMenuItem m_menuItem = null;

        /// <summary>
        /// Plugin initialization
        /// </summary>
        /// <param name="host">KeePass plugin host interface</param>
        /// <returns>True if initialization successful</returns>
        public override bool Initialize(IPluginHost host)
        {
            if (host == null) return false;

            m_host = host;

            try
            {
                // Initialize the key provider
                m_keyProvider = new HelloFIDOKeyProvider();
                m_host.KeyProviderPool.Add(m_keyProvider);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"HelloFIDO Plugin initialization failed: {ex.Message}", 
                    "HelloFIDO Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Plugin termination cleanup
        /// </summary>
        public override void Terminate()
        {
            try
            {
                // Remove the key provider
                if (m_keyProvider != null && m_host != null)
                {
                    m_host.KeyProviderPool.Remove(m_keyProvider);
                    m_keyProvider = null;
                }

                // Clean up menu item
                if (m_menuItem != null)
                {
                    m_menuItem.Dispose();
                    m_menuItem = null;
                }

                m_host = null;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"HelloFIDO Plugin termination error: {ex.Message}", 
                    "HelloFIDO Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// Provide menu item for plugin settings
        /// </summary>
        /// <param name="t">Menu type</param>
        /// <returns>Menu item or null</returns>
        public override ToolStripMenuItem GetMenuItem(PluginMenuType t)
        {
            if (t == PluginMenuType.Main)
            {
                m_menuItem = new ToolStripMenuItem();
                m_menuItem.Text = "HelloFIDO Settings";
                m_menuItem.Click += OnSettingsClicked;
                return m_menuItem;
            }

            return null;
        }

        /// <summary>
        /// Handle settings menu click
        /// </summary>
        private void OnSettingsClicked(object sender, EventArgs e)
        {
            try
            {
                using (var settingsForm = new SettingsForm())
                {
                    settingsForm.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening settings: {ex.Message}", 
                    "HelloFIDO Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Plugin update URL for version checking
        /// </summary>
        public override string UpdateUrl
        {
            get { return "https://github.com/user/HelloFIDO/releases/latest"; }
        }
    }
}
