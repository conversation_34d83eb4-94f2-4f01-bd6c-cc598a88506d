using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using Fido2NetLib;
using Fido2NetLib.Objects;
using Newtonsoft.Json;

namespace HelloFIDO
{
    /// <summary>
    /// Handles WebAuthn communication with browsers for FIDO2 operations
    /// </summary>
    public class WebAuthnBrowserCommunicator
    {
        private const int DefaultPort = 8765;
        private const string LocalServerUrl = "http://localhost:8765";
        private readonly HttpClient _httpClient;
        private Process _browserProcess;

        public WebAuthnBrowserCommunicator()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(60);
        }

        /// <summary>
        /// Create a new WebAuthn credential using browser communication
        /// </summary>
        public async Task<AuthenticatorAttestationRawResponse> CreateCredentialAsync(
            CredentialCreateOptions options, int timeoutMs = 60000)
        {
            try
            {
                // Start local WebAuthn server
                if (!await StartWebAuthnServerAsync())
                {
                    throw new InvalidOperationException("Failed to start WebAuthn server");
                }

                // Launch browser with WebAuthn page
                var browserUrl = $"{LocalServerUrl}/webauthn/create";
                await LaunchBrowserAsync(browserUrl);

                // Send credential creation options to browser
                var optionsJson = JsonConvert.SerializeObject(options, new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                });

                var response = await _httpClient.PostAsync($"{LocalServerUrl}/api/create-credential",
                    new StringContent(optionsJson, Encoding.UTF8, "application/json"));

                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<WebAuthnResponse<AuthenticatorAttestationRawResponse>>(responseJson);

                    if (result.Success)
                    {
                        return result.Data;
                    }
                    else
                    {
                        throw new InvalidOperationException($"WebAuthn creation failed: {result.Error}");
                    }
                }
                else
                {
                    throw new InvalidOperationException($"HTTP request failed: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"WebAuthn credential creation failed: {ex.Message}",
                    "FIDO2 Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
            finally
            {
                await StopWebAuthnServerAsync();
            }
        }

        /// <summary>
        /// Get WebAuthn assertion using browser communication
        /// </summary>
        public async Task<AuthenticatorAssertionRawResponse> GetAssertionAsync(
            AssertionOptions options, int timeoutMs = 60000)
        {
            try
            {
                // Start local WebAuthn server
                if (!await StartWebAuthnServerAsync())
                {
                    throw new InvalidOperationException("Failed to start WebAuthn server");
                }

                // Launch browser with WebAuthn page
                var browserUrl = $"{LocalServerUrl}/webauthn/authenticate";
                await LaunchBrowserAsync(browserUrl);

                // Send assertion options to browser
                var optionsJson = JsonConvert.SerializeObject(options, new JsonSerializerSettings
                {
                    NullValueHandling = NullValueHandling.Ignore
                });

                var response = await _httpClient.PostAsync($"{LocalServerUrl}/api/get-assertion",
                    new StringContent(optionsJson, Encoding.UTF8, "application/json"));

                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<WebAuthnResponse<AuthenticatorAssertionRawResponse>>(responseJson);

                    if (result.Success)
                    {
                        return result.Data;
                    }
                    else
                    {
                        throw new InvalidOperationException($"WebAuthn assertion failed: {result.Error}");
                    }
                }
                else
                {
                    throw new InvalidOperationException($"HTTP request failed: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"WebAuthn assertion failed: {ex.Message}",
                    "FIDO2 Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
            finally
            {
                await StopWebAuthnServerAsync();
            }
        }

        /// <summary>
        /// Start the local WebAuthn server for browser communication
        /// </summary>
        private async Task<bool> StartWebAuthnServerAsync()
        {
            try
            {
                // For now, return true - in a full implementation, you would:
                // 1. Start a local HTTP server on the specified port
                // 2. Serve WebAuthn HTML/JS pages
                // 3. Handle API endpoints for credential operations

                // Simulate server startup delay
                await Task.Delay(100);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Stop the local WebAuthn server
        /// </summary>
        private async Task StopWebAuthnServerAsync()
        {
            try
            {
                // Close browser if we opened it
                if (_browserProcess != null && !_browserProcess.HasExited)
                {
                    _browserProcess.CloseMainWindow();
                    if (!_browserProcess.WaitForExit(5000))
                    {
                        _browserProcess.Kill();
                    }
                    _browserProcess.Dispose();
                    _browserProcess = null;
                }

                // Simulate server shutdown delay
                await Task.Delay(100);
            }
            catch (Exception)
            {
                // Ignore cleanup errors
            }
        }

        /// <summary>
        /// Launch browser with WebAuthn page
        /// </summary>
        private async Task LaunchBrowserAsync(string url)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = url,
                    UseShellExecute = true
                };

                _browserProcess = Process.Start(startInfo);
                await Task.Delay(1000); // Give browser time to start
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to launch browser: {ex.Message}");
            }
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            _httpClient?.Dispose();
            _browserProcess?.Dispose();
        }
    }

    /// <summary>
    /// WebAuthn response wrapper
    /// </summary>
    public class WebAuthnResponse<T>
    {
        public bool Success { get; set; }
        public T Data { get; set; }
        public string Error { get; set; }
    }
}
