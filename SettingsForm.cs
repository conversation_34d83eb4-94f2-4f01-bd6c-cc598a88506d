using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace HelloFIDO
{
    /// <summary>
    /// Settings form for HelloFIDO plugin configuration
    /// </summary>
    public partial class SettingsForm : Form
    {
        private readonly WindowsHelloAuth _windowsHelloAuth;
        private readonly Fido2Auth _fido2Auth;

        public SettingsForm()
        {
            InitializeComponent();
            _windowsHelloAuth = new WindowsHelloAuth();
            _fido2Auth = new Fido2Auth();

            LoadSettings();
        }

        /// <summary>
        /// Load current settings and update UI
        /// </summary>
        private async void LoadSettings()
        {
            try
            {
                // Check Windows Hello availability
                bool helloAvailable = await _windowsHelloAuth.IsAvailableAsync();
                lblWindowsHelloStatus.Text = helloAvailable ? "Available" : "Not Available";
                lblWindowsHelloStatus.ForeColor = helloAvailable ? Color.Green : Color.Red;
                btnTestWindowsHello.Enabled = helloAvailable;

                // Check FIDO2 availability
                bool fido2Available = _fido2Auth.IsAvailable();
                lblFido2Status.Text = fido2Available ? "Available" : "Not Available";
                lblFido2Status.ForeColor = fido2Available ? Color.Green : Color.Red;
                btnTestFido2.Enabled = fido2Available;

                // Load FIDO2 authenticators
                var authenticators = _fido2Auth.GetAvailableAuthenticators();
                lstAuthenticators.Items.Clear();
                foreach (var auth in authenticators)
                {
                    lstAuthenticators.Items.Add(auth);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading settings: {ex.Message}",
                    "Settings Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// Test Windows Hello authentication
        /// </summary>
        private async void btnTestWindowsHello_Click(object sender, EventArgs e)
        {
            try
            {
                btnTestWindowsHello.Enabled = false;
                btnTestWindowsHello.Text = "Testing...";

                bool result = await _windowsHelloAuth.AuthenticateAsync("Test Windows Hello authentication");

                MessageBox.Show(result ? "Windows Hello test successful!" : "Windows Hello test failed or cancelled.",
                    "Test Result", MessageBoxButtons.OK,
                    result ? MessageBoxIcon.Information : MessageBoxIcon.Warning);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Windows Hello test error: {ex.Message}",
                    "Test Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnTestWindowsHello.Text = "Test Windows Hello";
                btnTestWindowsHello.Enabled = true;
            }
        }

        /// <summary>
        /// Test FIDO2 authentication
        /// </summary>
        private async void btnTestFido2_Click(object sender, EventArgs e)
        {
            try
            {
                btnTestFido2.Enabled = false;
                btnTestFido2.Text = "Testing...";

                // Show available authenticators
                var authenticators = await _fido2Auth.GetAvailableAuthenticatorsAsync();
                var availableCount = authenticators.Count(a => a.IsAvailable);

                var message = $"🔍 Found {availableCount} available authenticator(s):\n\n";
                foreach (var auth in authenticators.Where(a => a.IsAvailable))
                {
                    message += $"✅ {auth.Name} ({auth.Type})\n";
                    message += $"   {auth.Description}\n";
                    message += $"   User Verification: {(auth.SupportsUserVerification ? "Yes" : "No")}\n\n";
                }

                if (availableCount == 0)
                {
                    message += "❌ No authenticators are currently available.\n\n";
                    message += "Please ensure you have:\n";
                    message += "• Windows Hello set up, or\n";
                    message += "• A FIDO2 security key connected\n";

                    MessageBox.Show(message, "FIDO2 Authenticator Status",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                else
                {
                    message += "🧪 Would you like to test FIDO2 functionality?\n";
                    message += "(This will test both registration and authentication)";

                    var result = MessageBox.Show(message, "FIDO2 Authenticators Found",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Test with placeholder credential for now
                        byte[] testCredential = System.Text.Encoding.UTF8.GetBytes("test_credential");
                        bool authResult = await _fido2Auth.AuthenticateAsync(testCredential);

                        MessageBox.Show(authResult ?
                            "🎉 FIDO2 test successful!\n\nYour FIDO2 setup is working correctly." :
                            "⚠️ FIDO2 test failed or was cancelled.\n\nPlease try again or check your authenticator.",
                            "FIDO2 Test Result", MessageBoxButtons.OK,
                            authResult ? MessageBoxIcon.Information : MessageBoxIcon.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"FIDO2 test error: {ex.Message}\n\n" +
                    "This could be due to:\n" +
                    "• Browser communication issues\n" +
                    "• Authenticator hardware problems\n" +
                    "• Network or security restrictions",
                    "FIDO2 Test Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnTestFido2.Text = "Test FIDO2";
                btnTestFido2.Enabled = true;
            }
        }

        /// <summary>
        /// Register new FIDO2 authenticator
        /// </summary>
        private async void btnRegisterFido2_Click(object sender, EventArgs e)
        {
            try
            {
                btnRegisterFido2.Enabled = false;
                btnRegisterFido2.Text = "Registering...";

                string username = Environment.UserName;
                byte[] credentialData = await _fido2Auth.RegisterAsync(username, "KeePass User");

                if (credentialData != null)
                {
                    MessageBox.Show("FIDO2 authenticator registered successfully!\n" +
                        "Note: This is a demonstration. In a real implementation, " +
                        "the credential would be securely stored.",
                        "Registration Successful", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("FIDO2 authenticator registration failed.",
                        "Registration Failed", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"FIDO2 registration error: {ex.Message}",
                    "Registration Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnRegisterFido2.Text = "Register New";
                btnRegisterFido2.Enabled = true;
            }
        }

        /// <summary>
        /// Show about information
        /// </summary>
        private void btnAbout_Click(object sender, EventArgs e)
        {
            MessageBox.Show(
                "HelloFIDO Plugin for KeePass\n" +
                "Version 1.0.0\n\n" +
                "This plugin provides Windows Hello and FIDO2/WebAuthn authentication " +
                "for KeePass databases.\n\n" +
                "Features:\n" +
                "• Windows Hello biometric authentication\n" +
                "• FIDO2/WebAuthn security key support\n" +
                "• Fallback authentication methods\n" +
                "• Secure key storage\n\n" +
                "For more information and updates, visit:\n" +
                "https://github.com/user/HelloFIDO",
                "About HelloFIDO",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        /// <summary>
        /// Close the settings form
        /// </summary>
        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
