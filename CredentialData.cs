using System;
using Newtonsoft.Json;

namespace HelloFIDO
{
    /// <summary>
    /// Represents stored FIDO2 credential data
    /// </summary>
    public class CredentialData
    {
        /// <summary>
        /// Unique identifier for the credential
        /// </summary>
        [JsonProperty("credentialId")]
        public byte[] CredentialId { get; set; }

        /// <summary>
        /// Public key associated with the credential
        /// </summary>
        [JsonProperty("publicKey")]
        public byte[] PublicKey { get; set; }

        /// <summary>
        /// User handle (user ID) for the credential
        /// </summary>
        [JsonProperty("userHandle")]
        public byte[] UserHandle { get; set; }

        /// <summary>
        /// Signature counter for replay protection
        /// </summary>
        [JsonProperty("signatureCounter")]
        public uint SignatureCounter { get; set; }

        /// <summary>
        /// When the credential was created
        /// </summary>
        [JsonProperty("createdAt")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// When the credential was last used
        /// </summary>
        [JsonProperty("lastUsed")]
        public DateTime? LastUsed { get; set; }

        /// <summary>
        /// Friendly name for the credential
        /// </summary>
        [JsonProperty("friendlyName")]
        public string FriendlyName { get; set; }

        /// <summary>
        /// Type of authenticator (platform, cross-platform, etc.)
        /// </summary>
        [JsonProperty("authenticatorType")]
        public string AuthenticatorType { get; set; }

        /// <summary>
        /// AAGUID of the authenticator
        /// </summary>
        [JsonProperty("aaguid")]
        public Guid? AAGUID { get; set; }

        /// <summary>
        /// Validate the credential data
        /// </summary>
        public bool IsValid()
        {
            return CredentialId != null && CredentialId.Length > 0 &&
                   PublicKey != null && PublicKey.Length > 0 &&
                   UserHandle != null && UserHandle.Length > 0;
        }

        /// <summary>
        /// Get a human-readable description of the credential
        /// </summary>
        public string GetDescription()
        {
            var description = FriendlyName ?? "FIDO2 Credential";
            
            if (!string.IsNullOrEmpty(AuthenticatorType))
            {
                description += $" ({AuthenticatorType})";
            }
            
            if (CreatedAt != default)
            {
                description += $" - Created: {CreatedAt:yyyy-MM-dd}";
            }
            
            if (LastUsed.HasValue)
            {
                description += $" - Last used: {LastUsed:yyyy-MM-dd}";
            }
            
            return description;
        }

        /// <summary>
        /// Create a copy of the credential data
        /// </summary>
        public CredentialData Clone()
        {
            var json = JsonConvert.SerializeObject(this);
            return JsonConvert.DeserializeObject<CredentialData>(json);
        }
    }
}
