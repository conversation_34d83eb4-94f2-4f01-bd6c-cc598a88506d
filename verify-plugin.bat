@echo off
echo Verifying HelloFIDO Plugin Installation...
echo.

echo Checking if all required files are present:
echo.

set "PLUGINS_DIR=KeePass-Portable\Plugins"

echo Main plugin:
if exist "%PLUGINS_DIR%\HelloFIDO.dll" (
    echo [OK] HelloFIDO.dll
) else (
    echo [MISSING] HelloFIDO.dll
)

echo.
echo FIDO2 dependencies:
if exist "%PLUGINS_DIR%\Fido2.dll" (
    echo [OK] Fido2.dll
) else (
    echo [MISSING] Fido2.dll
)

if exist "%PLUGINS_DIR%\Fido2.Models.dll" (
    echo [OK] Fido2.Models.dll
) else (
    echo [MISSING] Fido2.Models.dll
)

if exist "%PLUGINS_DIR%\CBOR.dll" (
    echo [OK] CBOR.dll
) else (
    echo [MISSING] CBOR.dll
)

if exist "%PLUGINS_DIR%\NSec.Cryptography.dll" (
    echo [OK] NSec.Cryptography.dll
) else (
    echo [MISSING] NSec.Cryptography.dll
)

echo.
echo System dependencies:
if exist "%PLUGINS_DIR%\System.Memory.dll" (
    echo [OK] System.Memory.dll
) else (
    echo [MISSING] System.Memory.dll
)

if exist "%PLUGINS_DIR%\System.Buffers.dll" (
    echo [OK] System.Buffers.dll
) else (
    echo [MISSING] System.Buffers.dll
)

if exist "%PLUGINS_DIR%\Newtonsoft.Json.dll" (
    echo [OK] Newtonsoft.Json.dll
) else (
    echo [MISSING] Newtonsoft.Json.dll
)

echo.
echo Plugin verification complete.
echo.
echo To test the plugin:
echo 1. Start KeePass (run: cd KeePass-Portable && KeePass.exe)
echo 2. Check Tools menu for "HelloFIDO Settings"
echo 3. When creating/opening databases, look for "HelloFIDO (Windows Hello + FIDO2)" in key providers
echo.
pause
