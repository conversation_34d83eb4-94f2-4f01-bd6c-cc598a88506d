# HelloFIDO - KeePass Plugin

A KeePass plugin that implements Windows Hello and FIDO2/WebAuthn authentication for enhanced security and convenience.

## Features

- **Windows Hello Integration**: Use fingerprint, face recognition, or PIN for database authentication
- **FIDO2/WebAuthn Support**: Compatible with external security keys (<PERSON><PERSON><PERSON><PERSON>, etc.)
- **Dual Authentication**: Primary Windows Hello with FIDO2 fallback
- **Secure Key Storage**: Deterministic key generation based on authentication
- **Easy Configuration**: Simple settings interface for testing and setup

## Requirements

- Windows 10/11 with Windows Hello support
- KeePass 2.x
- .NET Framework 4.8.1 or later
- Windows Hello configured (for biometric authentication)
- FIDO2-compatible security key (optional, for external authentication)

## Installation

### Automated Installation (Recommended)

1. **Build and Deploy**:
   ```batch
   build.bat
   ```
   This will automatically build the plugin and copy all dependencies to the KeePass plugins folder.

2. **Test the Plugin**:
   ```batch
   test.bat
   ```
   This will start KeePass with the plugin loaded for testing.

### Manual Installation

1. **Build the Plugin**:
   ```batch
   build.bat
   ```

2. **Copy Files to KeePass**:
   Copy the following files from `bin\Debug\` to your KeePass `Plugins` folder:
   - `HelloFIDO.dll` (main plugin)
   - `Fido2.dll` and `Fido2.Models.dll` (FIDO2 support)
   - `CBOR.dll`, `NSec.Cryptography.dll`, `Newtonsoft.Json.dll` (crypto dependencies)
   - `Microsoft.IdentityModel.*.dll`, `System.IdentityModel.Tokens.Jwt.dll` (JWT support)
   - `System.Memory.dll`, `System.Buffers.dll`, `System.Runtime.CompilerServices.Unsafe.dll` (system dependencies)
   - Additional system libraries as needed

3. **Restart KeePass** to load the plugin

### Verification

Run the verification script to check if all dependencies are properly installed:
```batch
verify-plugin.bat
```

## Usage

### Setting up Authentication

1. Open KeePass and go to **Tools → HelloFIDO Settings**
2. Test Windows Hello and FIDO2 functionality
3. Register FIDO2 authenticators if needed

### Using with Databases

1. **Creating a New Database**:
   - In the "Create Composite Master Key" dialog
   - Check "Key file / provider"
   - Select "HelloFIDO (Windows Hello + FIDO2)" from dropdown
   - Choose your preferred authentication method

2. **Opening an Existing Database**:
   - Select the HelloFIDO key provider
   - Authenticate using Windows Hello or FIDO2
   - Database unlocks automatically upon successful authentication

## Authentication Methods

### Windows Hello
- **Primary Method**: Biometric authentication (fingerprint, face)
- **Requirements**: Windows Hello must be set up in Windows Settings
- **Fallback**: PIN authentication if biometrics fail

### FIDO2/WebAuthn
- **External Keys**: YubiKey, SoloKey, and other FIDO2 devices
- **Platform Authenticators**: Built-in security chips
- **Standards Compliant**: Full WebAuthn specification support

## Development

### Building from Source

1. **Prerequisites**:
   - Visual Studio 2019/2022 or Build Tools
   - .NET Framework 4.8.1 SDK
   - NuGet CLI or .NET SDK

2. **Build Commands**:
   ```batch
   # Clean previous builds
   clean.bat
   
   # Build debug and release versions
   build.bat
   
   # Test with KeePass portable
   test.bat
   ```

### Project Structure

```
HelloFIDO/
├── HelloFIDOExt.cs          # Main plugin class
├── WindowsHelloAuth.cs      # Windows Hello implementation
├── Fido2Auth.cs            # FIDO2/WebAuthn implementation
├── HelloFIDOKeyProvider.cs  # KeePass key provider
├── SettingsForm.cs         # Configuration UI
├── build.bat               # Build script
├── test.bat               # Testing script
└── clean.bat              # Cleanup script
```

### Dependencies

- **Microsoft.Windows.SDK.Contracts** (10.0.22621.2428): Windows Hello APIs
- **Fido2** (2.0.2): FIDO2/WebAuthn implementation
- **KeePass.exe**: KeePass plugin interfaces (referenced from portable version)

## Security Considerations

### Key Generation
- Uses deterministic key generation based on:
  - Database path
  - Authentication method
  - Machine-specific identifiers
  - User account information

### Data Protection
- No sensitive data stored in plain text
- Leverages Windows security infrastructure
- FIDO2 credentials stored securely by authenticators

### Authentication Flow
1. User selects HelloFIDO key provider
2. Plugin prompts for authentication method
3. Windows Hello or FIDO2 authentication performed
4. Deterministic key generated upon successful authentication
5. KeePass uses generated key to unlock database

## Troubleshooting

### Plugin Loading Issues

**Error: "Could not load file or assembly 'Fido2' or one of its dependencies"**
- **Cause**: Missing NuGet package dependencies in KeePass plugins folder
- **Solution**: Run `copy-deps.bat` or manually copy all DLL files from `bin\Debug\` to `KeePass-Portable\Plugins\`
- **Prevention**: Always use `build.bat` which automatically copies dependencies

**Error: "Plugin is incompatible with current KeePass version"**
- **Cause**: Missing dependencies or incorrect .NET Framework version
- **Solution**: Ensure all dependencies are copied and .NET Framework 4.8.1 is installed
- **Verification**: Run `verify-plugin.bat` to check all required files

### Windows Hello Issues
- Ensure Windows Hello is set up in Windows Settings
- Check that biometric sensors are working
- Verify no group policy restrictions
- Try PIN fallback if biometrics fail
- Restart Windows Hello service if needed

### FIDO2 Issues
- Ensure security key is properly connected
- Check for driver updates
- Verify key is FIDO2/WebAuthn compatible
- Test key with other applications
- Try different USB ports for USB keys

### Plugin Issues
- Check KeePass plugin folder permissions
- Verify .NET Framework 4.8.1 is installed
- Review Windows Event Logs for errors
- Enable KeePass debug mode for detailed logging
- Ensure KeePass is completely closed before copying new plugin files

## Known Limitations

1. **FIDO2 Implementation**: Current version includes placeholder FIDO2 implementation
2. **Browser Integration**: Full WebAuthn requires browser integration
3. **Credential Storage**: FIDO2 credentials need secure storage implementation
4. **Cross-Platform**: Windows-only due to Windows Hello dependency

## Future Enhancements

- [ ] Complete FIDO2/WebAuthn browser integration
- [ ] Secure credential storage for FIDO2 keys
- [ ] Multi-factor authentication combinations
- [ ] Backup authentication methods
- [ ] Enterprise policy support
- [ ] Audit logging and compliance features

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly with KeePass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues, questions, or contributions:
- Create an issue on GitHub
- Check existing documentation
- Test with the provided batch scripts
- Include detailed error information

## Acknowledgments

- KeePass development team for the excellent plugin architecture
- Microsoft for Windows Hello APIs
- FIDO Alliance for FIDO2/WebAuthn standards
- Open source community for FIDO2 .NET library
