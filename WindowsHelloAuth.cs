using System;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using System.Diagnostics;
using System.Text;

namespace HelloFIDO
{
    /// <summary>
    /// Windows Hello authentication implementation with real Windows Hello APIs
    /// </summary>
    public class WindowsHelloAuth
    {
        private const string KeyCredentialName = "HelloFIDO_KeePass_Database_Access";

        // Win32 API declarations for Windows Hello/Credential authentication
        [DllImport("credui.dll", CharSet = CharSet.Unicode)]
        private static extern int CredUIPromptForWindowsCredentials(
            ref CREDUI_INFO pUiInfo,
            int dwAuthError,
            ref uint pulAuthPackage,
            IntPtr pvInAuthBuffer,
            uint ulInAuthBufferSize,
            out IntPtr ppvOutAuthBuffer,
            out uint pulOutAuthBufferSize,
            ref bool pfSave,
            int dwFlags);

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        private struct CREDUI_INFO
        {
            public int cbSize;
            public IntPtr hwndParent;
            public string pszMessageText;
            public string pszCaptionText;
            public IntPtr hbmBanner;
        }

        // Constants for CredUIPromptForWindowsCredentials
        private const int CREDUIWIN_GENERIC = 0x1;
        private const int CREDUIWIN_CHECKBOX = 0x2;
        private const int CREDUIWIN_AUTHPACKAGE_ONLY = 0x10;
        private const int CREDUIWIN_IN_CRED_ONLY = 0x20;
        private const int CREDUIWIN_ENUMERATE_ADMINS = 0x100;
        private const int CREDUIWIN_ENUMERATE_CURRENT_USER = 0x200;
        private const int CREDUIWIN_SECURE_PROMPT = 0x1000;
        private const int CREDUIWIN_PACK_32_WOW = 0x10000000;

        // Windows Biometric Framework API declarations
        [DllImport("winbio.dll", SetLastError = true)]
        private static extern int WinBioEnumBiometricUnits(
            uint factor,
            out IntPtr unitSchemaArray,
            out uint unitCount);

        [DllImport("winbio.dll", SetLastError = true)]
        private static extern int WinBioOpenSession(
            uint factor,
            uint poolType,
            uint flags,
            IntPtr unitArray,
            uint unitCount,
            IntPtr databaseId,
            out IntPtr sessionHandle);

        [DllImport("winbio.dll", SetLastError = true)]
        private static extern int WinBioVerify(
            IntPtr sessionHandle,
            ref WINBIO_IDENTITY identity,
            uint subFactor,
            out IntPtr unitId,
            out bool match,
            out uint rejectDetail);

        [DllImport("winbio.dll", SetLastError = true)]
        private static extern int WinBioCloseSession(IntPtr sessionHandle);

        [DllImport("winbio.dll", SetLastError = true)]
        private static extern void WinBioFree(IntPtr memory);

        // Constants for Windows Biometric Framework
        private const uint WINBIO_TYPE_FINGERPRINT = 0x00000008;
        private const uint WINBIO_POOL_SYSTEM = 0x00000001;
        private const uint WINBIO_FLAG_DEFAULT = 0x00000000;

        [StructLayout(LayoutKind.Sequential)]
        private struct WINBIO_IDENTITY
        {
            public uint Type;
            public WINBIO_IDENTITY_VALUE Value;
        }

        [StructLayout(LayoutKind.Explicit)]
        private struct WINBIO_IDENTITY_VALUE
        {
            [FieldOffset(0)]
            public uint Null;
            [FieldOffset(0)]
            public uint Wildcard;
            [FieldOffset(0)]
            public Guid TemplateGuid;
            [FieldOffset(0)]
            public WINBIO_SID AccountSid;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct WINBIO_SID
        {
            public uint Size;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 68)]
            public byte[] Data;
        }

        /// <summary>
        /// Check if Windows Hello is available using real Windows APIs
        /// </summary>
        /// <returns>True if Windows Hello is supported and available</returns>
        public async Task<bool> IsAvailableAsync()
        {
            try
            {
                // Check OS version first
                var osVersion = Environment.OSVersion;
                if (osVersion.Platform != PlatformID.Win32NT || osVersion.Version.Major < 10)
                {
                    return false;
                }

                // Check if biometric framework is available
                bool biometricAvailable = await Task.Run(() =>
                {
                    try
                    {
                        // Try to enumerate biometric units
                        uint unitCount = 0;
                        int result = WinBioEnumBiometricUnits(
                            WINBIO_TYPE_FINGERPRINT,
                            out IntPtr unitSchemaArray,
                            out unitCount);

                        if (unitSchemaArray != IntPtr.Zero)
                        {
                            WinBioFree(unitSchemaArray);
                        }

                        return result == 0 && unitCount > 0; // S_OK and units found
                    }
                    catch
                    {
                        return false;
                    }
                });

                if (biometricAvailable)
                {
                    return true;
                }

                // Fallback: Check if Windows Hello PIN is available
                return await Task.Run(() =>
                {
                    try
                    {
                        // Check if credential provider is available
                        var startInfo = new ProcessStartInfo
                        {
                            FileName = "cmd.exe",
                            Arguments = "/c reg query \"HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Authentication\\Credential Providers\\{D6886603-9D2F-4EB2-B667-1971041FA96B}\" /ve",
                            UseShellExecute = false,
                            RedirectStandardOutput = true,
                            CreateNoWindow = true
                        };

                        using (var process = Process.Start(startInfo))
                        {
                            process.WaitForExit();
                            return process.ExitCode == 0;
                        }
                    }
                    catch
                    {
                        return false;
                    }
                });
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Authenticate using real Windows Hello biometric authentication
        /// </summary>
        /// <param name="message">Optional message to display during authentication</param>
        /// <returns>True if authentication successful</returns>
        public async Task<bool> AuthenticateAsync(string message = null)
        {
            try
            {
                // Check if Windows Hello is available
                if (!await IsAvailableAsync())
                {
                    MessageBox.Show("Windows Hello is not available on this device. Please ensure:\n" +
                        "- Windows Hello is set up in Windows Settings\n" +
                        "- A PIN, fingerprint, or face recognition is configured\n" +
                        "- The device supports Windows Hello",
                        "Windows Hello Not Available",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }

                // Use provided message or default
                string authMessage = message ?? "Authenticate with Windows Hello to unlock KeePass database";

                // Method 1: Try real biometric authentication using Windows Biometric Framework
                bool biometricResult = await Task.Run(() =>
                {
                    try
                    {
                        IntPtr sessionHandle = IntPtr.Zero;

                        // Open biometric session
                        int result = WinBioOpenSession(
                            WINBIO_TYPE_FINGERPRINT,
                            WINBIO_POOL_SYSTEM,
                            WINBIO_FLAG_DEFAULT,
                            IntPtr.Zero,
                            0,
                            IntPtr.Zero,
                            out sessionHandle);

                        if (result != 0 || sessionHandle == IntPtr.Zero)
                        {
                            return false;
                        }

                        try
                        {
                            // Show authentication prompt on UI thread
                            // Note: This will be shown before the biometric prompt

                            // Create identity for current user
                            var identity = new WINBIO_IDENTITY
                            {
                                Type = 1, // WINBIO_ID_TYPE_SID
                                Value = new WINBIO_IDENTITY_VALUE()
                            };

                            // Perform biometric verification
                            IntPtr unitId;
                            bool match;
                            uint rejectDetail;

                            result = WinBioVerify(
                                sessionHandle,
                                ref identity,
                                0, // Any finger
                                out unitId,
                                out match,
                                out rejectDetail);

                            return result == 0 && match;
                        }
                        finally
                        {
                            // Always close the session
                            WinBioCloseSession(sessionHandle);
                        }
                    }
                    catch (Exception)
                    {
                        return false;
                    }
                });

                if (biometricResult)
                {
                    return true;
                }

                // Method 2: Fallback to Windows Credential UI with biometric support
                var credInfo = new CREDUI_INFO
                {
                    cbSize = Marshal.SizeOf(typeof(CREDUI_INFO)),
                    hwndParent = IntPtr.Zero,
                    pszMessageText = authMessage + "\n\nUse Windows Hello (PIN, fingerprint, or face recognition)",
                    pszCaptionText = "Windows Hello Authentication",
                    hbmBanner = IntPtr.Zero
                };

                uint authPackage = 0;
                IntPtr outCredBuffer = IntPtr.Zero;
                uint outCredSize = 0;
                bool save = false;

                int credResult = CredUIPromptForWindowsCredentials(
                    ref credInfo,
                    0,
                    ref authPackage,
                    IntPtr.Zero,
                    0,
                    out outCredBuffer,
                    out outCredSize,
                    ref save,
                    CREDUIWIN_GENERIC | CREDUIWIN_SECURE_PROMPT);

                if (outCredBuffer != IntPtr.Zero)
                {
                    Marshal.ZeroFreeGlobalAllocUnicode(outCredBuffer);
                }

                if (credResult == 0)
                {
                    return true;
                }
                else if (credResult == 1223)
                {
                    return false; // User cancelled
                }
                else
                {
                    MessageBox.Show($"Windows Hello authentication failed (Error: {credResult}).\n\n" +
                        "Please ensure Windows Hello is properly configured and try again.",
                        "Windows Hello Authentication Failed",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Windows Hello authentication error: {ex.Message}\n\n" +
                    "Please try again or use your master password to unlock the database.",
                    "Windows Hello Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Create a persistent credential for storing encrypted data (simplified implementation)
        /// </summary>
        /// <param name="credentialName">Name for the credential</param>
        /// <returns>True if credential created successfully</returns>
        public async Task<bool> CreatePersistentCredentialAsync(string credentialName = null)
        {
            try
            {
                string name = credentialName ?? KeyCredentialName;

                // Simplified implementation - always return true for demo
                // In a full implementation, this would create actual Windows Hello credentials
                return await Task.FromResult(true);
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Delete a persistent credential (simplified implementation)
        /// </summary>
        /// <param name="credentialName">Name of the credential to delete</param>
        /// <returns>True if deletion successful</returns>
        public async Task<bool> DeleteCredentialAsync(string credentialName = null)
        {
            try
            {
                string name = credentialName ?? KeyCredentialName;

                // Simplified implementation - always return true for demo
                // In a full implementation, this would delete actual Windows Hello credentials
                return await Task.FromResult(true);
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Check if a persistent credential exists (simplified implementation)
        /// </summary>
        /// <param name="credentialName">Name of the credential to check</param>
        /// <returns>True if credential exists</returns>
        public async Task<bool> CredentialExistsAsync(string credentialName = null)
        {
            try
            {
                string name = credentialName ?? KeyCredentialName;

                // Simplified implementation - always return true for demo
                // In a full implementation, this would check for actual Windows Hello credentials
                return await Task.FromResult(true);
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Run a PowerShell command and return the output
        /// </summary>
        /// <param name="command">PowerShell command to execute</param>
        /// <returns>Command output</returns>
        private async Task<string> RunPowerShellCommandAsync(string command)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "powershell.exe",
                    Arguments = $"-Command \"{command}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(startInfo))
                {
                    var output = await process.StandardOutput.ReadToEndAsync();
                    var error = await process.StandardError.ReadToEndAsync();

                    await Task.Run(() => process.WaitForExit());

                    if (process.ExitCode == 0)
                    {
                        return output.Trim();
                    }
                    else
                    {
                        return $"Error: {error}";
                    }
                }
            }
            catch (Exception ex)
            {
                return $"Exception: {ex.Message}";
            }
        }
    }
}
