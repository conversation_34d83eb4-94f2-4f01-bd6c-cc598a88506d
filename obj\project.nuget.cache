{"version": 2, "dgSpecHash": "d5jZKv835pA=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\source\\repos\\HelloFIDO\\HelloFIDO.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\fido2\\2.0.2\\fido2.2.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fido2.models\\2.0.2\\fido2.models.2.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\libsodium\\1.0.18\\libsodium.1.0.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\6.6.0\\microsoft.identitymodel.jsonwebtokens.6.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\6.6.0\\microsoft.identitymodel.logging.6.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\6.6.0\\microsoft.identitymodel.tokens.6.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.0.1\\microsoft.netcore.platforms.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.contracts\\10.0.22621.2428\\microsoft.windows.sdk.contracts.10.0.22621.2428.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\12.0.3\\newtonsoft.json.12.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nsec.cryptography\\20.2.0\\nsec.cryptography.20.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\petero.cbor\\4.1.3\\petero.cbor.4.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\petero.numbers\\1.6.0\\petero.numbers.1.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\petero.uriutility\\1.0.0\\petero.uriutility.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\6.6.0\\system.identitymodel.tokens.jwt.6.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\4.7.0\\system.runtime.compilerservices.unsafe.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.interopservices.windowsruntime\\4.3.0\\system.runtime.interopservices.windowsruntime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.windowsruntime\\4.6.0\\system.runtime.windowsruntime.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.windowsruntime.ui.xaml\\4.6.0\\system.runtime.windowsruntime.ui.xaml.4.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\4.7.0\\system.security.cryptography.cng.4.7.0.nupkg.sha512"], "logs": []}