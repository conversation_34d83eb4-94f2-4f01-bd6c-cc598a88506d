﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)system.runtime.windowsruntime\4.6.0\buildTransitive\net461\System.Runtime.WindowsRuntime.targets" Condition="Exists('$(NuGetPackageRoot)system.runtime.windowsruntime\4.6.0\buildTransitive\net461\System.Runtime.WindowsRuntime.targets')" />
    <Import Project="$(NuGetPackageRoot)system.runtime.windowsruntime.ui.xaml\4.6.0\build\net461\System.Runtime.WindowsRuntime.UI.Xaml.targets" Condition="Exists('$(NuGetPackageRoot)system.runtime.windowsruntime.ui.xaml\4.6.0\build\net461\System.Runtime.WindowsRuntime.UI.Xaml.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.windows.sdk.contracts\10.0.22621.2428\build\Microsoft.Windows.SDK.Contracts.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windows.sdk.contracts\10.0.22621.2428\build\Microsoft.Windows.SDK.Contracts.targets')" />
    <Import Project="$(NuGetPackageRoot)fido2\2.0.2\build\fido2.targets" Condition="Exists('$(NuGetPackageRoot)fido2\2.0.2\build\fido2.targets')" />
  </ImportGroup>
</Project>