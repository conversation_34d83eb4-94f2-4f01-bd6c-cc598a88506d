using System;

namespace HelloFIDO
{
    /// <summary>
    /// Information about an available FIDO2 authenticator
    /// </summary>
    public class AuthenticatorInfo
    {
        /// <summary>
        /// Display name of the authenticator
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Type of authenticator (Platform, Cross-Platform)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Human-readable description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Whether the authenticator is currently available
        /// </summary>
        public bool IsAvailable { get; set; }

        /// <summary>
        /// Whether the authenticator supports user verification
        /// </summary>
        public bool SupportsUserVerification { get; set; }

        /// <summary>
        /// AAGUID of the authenticator (if known)
        /// </summary>
        public Guid? AAGUID { get; set; }

        /// <summary>
        /// Transport methods supported (usb, nfc, ble, internal)
        /// </summary>
        public string[] SupportedTransports { get; set; }

        /// <summary>
        /// Whether this is a platform authenticator
        /// </summary>
        public bool IsPlatformAuthenticator => Type?.Equals("Platform", StringComparison.OrdinalIgnoreCase) == true;

        /// <summary>
        /// Whether this is a cross-platform authenticator
        /// </summary>
        public bool IsCrossPlatformAuthenticator => Type?.Equals("Cross-Platform", StringComparison.OrdinalIgnoreCase) == true;

        /// <summary>
        /// Get a display string for the authenticator
        /// </summary>
        public override string ToString()
        {
            var status = IsAvailable ? "Available" : "Not Available";
            return $"{Name} ({Type}) - {status}";
        }

        /// <summary>
        /// Get detailed information string
        /// </summary>
        public string GetDetailedInfo()
        {
            var info = $"{Name}\n";
            info += $"Type: {Type}\n";
            info += $"Description: {Description}\n";
            info += $"Available: {(IsAvailable ? "Yes" : "No")}\n";
            info += $"User Verification: {(SupportsUserVerification ? "Supported" : "Not Supported")}\n";
            
            if (SupportedTransports != null && SupportedTransports.Length > 0)
            {
                info += $"Transports: {string.Join(", ", SupportedTransports)}\n";
            }
            
            if (AAGUID.HasValue)
            {
                info += $"AAGUID: {AAGUID.Value}\n";
            }
            
            return info.TrimEnd('\n');
        }
    }
}
