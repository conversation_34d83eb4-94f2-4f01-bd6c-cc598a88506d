@echo off
echo Cleaning HelloFIDO project...
echo.

REM Remove build outputs
if exist "bin" (
    echo Removing bin folder...
    rmdir /s /q "bin"
)

if exist "obj" (
    echo Removing obj folder...
    rmdir /s /q "obj"
)

REM Remove plugin from KeePass folder
if exist "KeePass-Portable\Plugins\HelloFIDO.dll" (
    echo Removing plugin from KeePass-Portable\Plugins...
    del "KeePass-Portable\Plugins\HelloFIDO.dll"
)

REM Remove packages folder if it exists
if exist "packages" (
    echo Removing packages folder...
    rmdir /s /q "packages"
)

REM Remove Visual Studio temporary files
if exist "*.suo" del "*.suo"
if exist "*.user" del "*.user"

REM Remove temporary files
for /r %%i in (*.tmp) do del "%%i" 2>nul
for /r %%i in (*~) do del "%%i" 2>nul

echo.
echo Cleanup completed.
echo.
pause
