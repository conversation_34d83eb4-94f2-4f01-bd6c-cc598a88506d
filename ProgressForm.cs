using System;
using System.Drawing;
using System.Windows.Forms;

namespace HelloFIDO
{
    /// <summary>
    /// Progress dialog for FIDO2 operations
    /// </summary>
    public partial class ProgressForm : Form
    {
        private Label _titleLabel;
        private Label _messageLabel;
        private ProgressBar _progressBar;
        private Button _cancelButton;
        private Timer _animationTimer;
        private int _animationStep = 0;

        public bool IsCancelled { get; private set; }

        public ProgressForm(string title, string message)
        {
            InitializeComponent();
            SetTitle(title);
            SetMessage(message);
            StartAnimation();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "FIDO2 Operation";
            this.Size = new Size(400, 200);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowInTaskbar = false;
            this.TopMost = true;

            // Title label
            _titleLabel = new Label
            {
                Text = "FIDO2 Operation",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                Location = new Point(20, 20),
                Size = new Size(360, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };
            this.Controls.Add(_titleLabel);

            // Message label
            _messageLabel = new Label
            {
                Text = "Please wait...",
                Font = new Font("Segoe UI", 9F),
                Location = new Point(20, 55),
                Size = new Size(360, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };
            this.Controls.Add(_messageLabel);

            // Progress bar
            _progressBar = new ProgressBar
            {
                Style = ProgressBarStyle.Marquee,
                MarqueeAnimationSpeed = 30,
                Location = new Point(20, 105),
                Size = new Size(360, 23)
            };
            this.Controls.Add(_progressBar);

            // Cancel button
            _cancelButton = new Button
            {
                Text = "Cancel",
                Location = new Point(162, 140),
                Size = new Size(75, 23),
                UseVisualStyleBackColor = true
            };
            _cancelButton.Click += CancelButton_Click;
            this.Controls.Add(_cancelButton);

            // Animation timer
            _animationTimer = new Timer
            {
                Interval = 500
            };
            _animationTimer.Tick += AnimationTimer_Tick;

            this.ResumeLayout(false);
        }

        public void SetTitle(string title)
        {
            if (_titleLabel != null)
            {
                _titleLabel.Text = title;
            }
        }

        public void SetMessage(string message)
        {
            if (_messageLabel != null)
            {
                _messageLabel.Text = message;
            }
        }

        public void SetProgress(int percentage)
        {
            if (_progressBar != null)
            {
                _progressBar.Style = ProgressBarStyle.Continuous;
                _progressBar.Value = Math.Max(0, Math.Min(100, percentage));
            }
        }

        private void StartAnimation()
        {
            _animationTimer?.Start();
        }

        private void StopAnimation()
        {
            _animationTimer?.Stop();
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            _animationStep++;
            var dots = new string('.', (_animationStep % 4));
            var baseMessage = _messageLabel.Text.TrimEnd('.');
            
            if (baseMessage.Contains("..."))
            {
                baseMessage = baseMessage.Substring(0, baseMessage.IndexOf("..."));
            }
            
            _messageLabel.Text = baseMessage + dots;
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            IsCancelled = true;
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            StopAnimation();
            base.OnFormClosing(e);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _animationTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
