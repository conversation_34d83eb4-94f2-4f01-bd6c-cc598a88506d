@echo off
echo Testing HelloFIDO KeePass Plugin...
echo.

REM Check if KeePass portable exists
if not exist "KeePass-Portable\KeePass.exe" (
    echo KeePass portable not found at KeePass-Portable\KeePass.exe
    echo Please ensure KeePass portable is extracted in the KeePass-Portable folder.
    pause
    exit /b 1
)

REM Create plugins folder if it doesn't exist
if not exist "KeePass-Portable\Plugins" (
    echo Creating Plugins folder...
    mkdir "KeePass-Portable\Plugins"
)

REM Check if plugin is built
if not exist "bin\Debug\HelloFIDO.dll" (
    echo Plugin not found. Building first...
    call build.bat
    if %ERRORLEVEL% NEQ 0 (
        echo Build failed. Cannot test plugin.
        pause
        exit /b 1
    )
)

REM Copy plugin and dependencies to KeePass plugins folder
echo Copying plugin and dependencies to KeePass plugins folder...
copy "bin\Debug\HelloFIDO.dll" "KeePass-Portable\Plugins\" >nul
copy "bin\Debug\Fido2.dll" "KeePass-Portable\Plugins\" >nul
copy "bin\Debug\Fido2.Models.dll" "KeePass-Portable\Plugins\" >nul
copy "bin\Debug\CBOR.dll" "KeePass-Portable\Plugins\" >nul
copy "bin\Debug\NSec.Cryptography.dll" "KeePass-Portable\Plugins\" >nul
copy "bin\Debug\Newtonsoft.Json.dll" "KeePass-Portable\Plugins\" >nul
copy "bin\Debug\Microsoft.IdentityModel.*.dll" "KeePass-Portable\Plugins\" >nul 2>nul
copy "bin\Debug\System.IdentityModel.Tokens.Jwt.dll" "KeePass-Portable\Plugins\" >nul
copy "bin\Debug\System.Memory.dll" "KeePass-Portable\Plugins\" >nul
copy "bin\Debug\System.Buffers.dll" "KeePass-Portable\Plugins\" >nul
copy "bin\Debug\System.Runtime.CompilerServices.Unsafe.dll" "KeePass-Portable\Plugins\" >nul
copy "bin\Debug\System.Numerics.Vectors.dll" "KeePass-Portable\Plugins\" >nul
copy "bin\Debug\System.Security.Cryptography.Cng.dll" "KeePass-Portable\Plugins\" >nul
copy "bin\Debug\Numbers.dll" "KeePass-Portable\Plugins\" >nul
copy "bin\Debug\URIUtility.dll" "KeePass-Portable\Plugins\" >nul
if %ERRORLEVEL% NEQ 0 (
    echo Failed to copy some plugin files. This may be normal for optional dependencies.
)

REM Create a test database if it doesn't exist
if not exist "TestDatabase.kdbx" (
    echo Creating test database...
    echo Note: You'll need to create a test database manually in KeePass
    echo with a simple password for testing purposes.
    echo.
)

echo Starting KeePass with HelloFIDO plugin...
echo.
echo Instructions for testing:
echo 1. KeePass will start with the HelloFIDO plugin loaded
echo 2. Create a new database or open an existing one
echo 3. Go to Tools menu to find "HelloFIDO Settings"
echo 4. Test Windows Hello and FIDO2 functionality
echo 5. Try using HelloFIDO as a key provider when opening/creating databases
echo.
echo Press any key to start KeePass...
pause >nul

REM Start KeePass
cd "KeePass-Portable"
start "" "KeePass.exe"
cd ..

echo KeePass started. Check the Tools menu for HelloFIDO Settings.
echo.
echo To test the key provider:
echo 1. Create a new database
echo 2. In the "Create Composite Master Key" dialog
echo 3. Check "Key file / provider" 
echo 4. Select "HelloFIDO (Windows Hello + FIDO2)" from the dropdown
echo 5. Follow the authentication prompts
echo.
echo Press any key to continue...
pause >nul
