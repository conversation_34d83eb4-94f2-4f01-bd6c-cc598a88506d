<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>58552796-edbd-4aa0-a4b0-b22e25964e33</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>HelloFIDO</RootNamespace>
    <AssemblyName>HelloFIDO</AssemblyName>
    <TargetFrameworkVersion>v4.8.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <RuntimeIdentifiers>win</RuntimeIdentifiers>
    <RestoreProjectStyle>PackageReference</RestoreProjectStyle>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System"/>
    <Reference Include="System.Core"/>
    <Reference Include="System.Xml.Linq"/>
    <Reference Include="System.Data.DataSetExtensions"/>
    <Reference Include="Microsoft.CSharp"/>
    <Reference Include="System.Data"/>
    <Reference Include="System.Net.Http"/>
    <Reference Include="System.Xml"/>
    <Reference Include="System.Windows.Forms"/>
    <Reference Include="System.Drawing"/>
    <Reference Include="KeePass">
      <HintPath>KeePass-Portable\KeePass.exe</HintPath>
      <Private>False</Private>
    </Reference>
    <!-- FIDO2 Package References -->
    <Reference Include="Fido2">
      <HintPath>$(NuGetPackageRoot)fido2\2.0.2\lib\netstandard2.0\Fido2.dll</HintPath>
    </Reference>
    <Reference Include="Fido2.Models">
      <HintPath>$(NuGetPackageRoot)fido2.models\2.0.2\lib\netstandard2.0\Fido2.Models.dll</HintPath>
    </Reference>
    <Reference Include="CBOR">
      <HintPath>$(NuGetPackageRoot)petero.cbor\4.1.3\lib\net40\CBOR.dll</HintPath>
    </Reference>
    <Reference Include="NSec.Cryptography">
      <HintPath>$(NuGetPackageRoot)nsec.cryptography\20.2.0\lib\netstandard2.0\NSec.Cryptography.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>$(NuGetPackageRoot)newtonsoft.json\12.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <!-- Microsoft Identity Model References -->
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens">
      <HintPath>$(NuGetPackageRoot)microsoft.identitymodel.jsonwebtokens\6.6.0\lib\net461\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging">
      <HintPath>$(NuGetPackageRoot)microsoft.identitymodel.logging\6.6.0\lib\net461\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens">
      <HintPath>$(NuGetPackageRoot)microsoft.identitymodel.tokens\6.6.0\lib\net461\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="System.IdentityModel.Tokens.Jwt">
      <HintPath>$(NuGetPackageRoot)system.identitymodel.tokens.jwt\6.6.0\lib\net461\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <!-- System References -->
    <Reference Include="System.Memory">
      <HintPath>$(NuGetPackageRoot)system.memory\4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>$(NuGetPackageRoot)system.buffers\4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>$(NuGetPackageRoot)system.runtime.compilerservices.unsafe\4.7.0\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>$(NuGetPackageRoot)system.numerics.vectors\4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Cng">
      <HintPath>$(NuGetPackageRoot)system.security.cryptography.cng\4.7.0\lib\net47\System.Security.Cryptography.Cng.dll</HintPath>
    </Reference>
    <Reference Include="Numbers">
      <HintPath>$(NuGetPackageRoot)petero.numbers\1.6.0\lib\net40\Numbers.dll</HintPath>
    </Reference>
    <Reference Include="URIUtility">
      <HintPath>$(NuGetPackageRoot)petero.uriutility\1.0.0\lib\net40\URIUtility.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Windows.SDK.Contracts" Version="10.0.22621.2428" />
    <PackageReference Include="Fido2" Version="2.0.2" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="HelloFIDOExt.cs" />
    <Compile Include="WindowsHelloAuth.cs" />
    <Compile Include="Fido2Auth.cs" />
    <Compile Include="HelloFIDOKeyProvider.cs" />
    <Compile Include="WebAuthnBrowserCommunicator.cs" />
    <Compile Include="CredentialData.cs" />
    <Compile Include="AuthenticatorInfo.cs" />
    <Compile Include="ProgressForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SettingsForm.Designer.cs">
      <DependentUpon>SettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="SettingsForm.resx">
      <DependentUpon>SettingsForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
