{"version": 3, "targets": {".NETFramework,Version=v4.8.1": {"Fido2/2.0.2": {"type": "package", "dependencies": {"Fido2.Models": "2.0.2", "NSec.Cryptography": "20.2.0", "Newtonsoft.Json": "12.0.3", "PeterO.Cbor": "4.1.3", "System.IdentityModel.Tokens.Jwt": "6.6.0", "System.Memory": "4.5.4", "System.Security.Cryptography.Cng": "4.7.0"}, "compile": {"lib/netstandard2.0/Fido2.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Fido2.dll": {"related": ".pdb"}}, "build": {"build/fido2.targets": {}}}, "Fido2.Models/2.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.3"}, "compile": {"lib/netstandard2.0/Fido2.Models.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Fido2.Models.dll": {"related": ".pdb;.xml"}}}, "libsodium/1.0.18": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1"}, "runtimeTargets": {"runtimes/linux-musl-x64/native/libsodium.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libsodium.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-x64/native/libsodium.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-x64/native/libsodium.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libsodium.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.6.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.6.0"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/6.6.0": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/6.6.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "6.6.0"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net461/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/1.0.1": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Windows.SDK.Contracts/10.0.22621.2428": {"type": "package", "dependencies": {"System.Runtime.InteropServices.WindowsRuntime": "4.3.0", "System.Runtime.WindowsRuntime": "4.6.0", "System.Runtime.WindowsRuntime.UI.Xaml": "4.6.0"}, "compile": {"ref/netstandard2.0/Windows.AI.MachineLearning.MachineLearningContract.winmd": {}, "ref/netstandard2.0/Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivatedEventsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.ContactActivatedEventsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsPhoneContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsVoipContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.LockScreenCallContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.FullTrustAppContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Search.Core.SearchCoreContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Search.SearchContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.SocialInfo.SocialInfoContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.StartupTaskContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Wallet.WalletContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Custom.CustomDeviceContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.DevicesLowLevelContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Portable.PortableDeviceContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Printers.Extensions.ExtensionsContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Printers.PrintersContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Scanners.ScannerDeviceContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardEmulatorContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Sms.LegacySmsApiContract.winmd": {}, "ref/netstandard2.0/Windows.Embedded.DeviceLockdown.DeviceLockdownContract.winmd": {}, "ref/netstandard2.0/Windows.Foundation.FoundationContract.winmd": {}, "ref/netstandard2.0/Windows.Foundation.UniversalApiContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.Input.GamingInputPreviewContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.Preview.GamesEnumerationContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.UI.GameChatOverlayContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.UI.GamingUIProviderContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.XboxLive.StorageApiContract.winmd": {}, "ref/netstandard2.0/Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.winmd": {}, "ref/netstandard2.0/Windows.Graphics.Printing3D.Printing3DContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Deployment.Preview.DeploymentPreviewContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Deployment.SharedPackageContainerContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Update.WindowsUpdateContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Workplace.WorkplaceSettingsContract.winmd": {}, "ref/netstandard2.0/Windows.Media.AppBroadcasting.AppBroadcastingContract.winmd": {}, "ref/netstandard2.0/Windows.Media.AppRecording.AppRecordingContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.AppBroadcastContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.AppCaptureContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.AppCaptureMetadataContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.CameraCaptureUIContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.GameBarContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Devices.CallControlContract.winmd": {}, "ref/netstandard2.0/Windows.Media.MediaControlContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Playlists.PlaylistsContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Protection.ProtectionRenewalContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.Connectivity.WwanContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.NetworkOperators.NetworkOperatorsFdnContract.WinMD": {}, "ref/netstandard2.0/Windows.Networking.Sockets.ControlChannelTriggerContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.winmd": {}, "ref/netstandard2.0/Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.winmd": {}, "ref/netstandard2.0/Windows.Phone.PhoneContract.winmd": {}, "ref/netstandard2.0/Windows.Phone.StartScreen.DualSimTileContract.WinMD": {}, "ref/netstandard2.0/Windows.Security.EnterpriseData.EnterpriseDataContract.winmd": {}, "ref/netstandard2.0/Windows.Security.ExchangeActiveSyncProvisioning.EasContract.winmd": {}, "ref/netstandard2.0/Windows.Security.Isolation.Isolatedwindowsenvironmentcontract.winmd": {}, "ref/netstandard2.0/Windows.Services.Maps.GuidanceContract.winmd": {}, "ref/netstandard2.0/Windows.Services.Maps.LocalSearchContract.winmd": {}, "ref/netstandard2.0/Windows.Services.Store.StoreContract.winmd": {}, "ref/netstandard2.0/Windows.Services.TargetedContent.TargetedContentContract.winmd": {}, "ref/netstandard2.0/Windows.Storage.Provider.CloudFilesContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.ProfileHardwareTokenContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.ProfileRetailInfoContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.ProfileSharedModeContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.winmd": {}, "ref/netstandard2.0/Windows.System.SystemManagementContract.winmd": {}, "ref/netstandard2.0/Windows.System.UserProfile.UserProfileContract.winmd": {}, "ref/netstandard2.0/Windows.System.UserProfile.UserProfileLockScreenContract.winmd": {}, "ref/netstandard2.0/Windows.UI.ApplicationSettings.ApplicationsSettingsContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Core.CoreWindowDialogsContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Shell.SecurityAppManagerContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Shell.WindowTabManagerContract.winmd": {}, "ref/netstandard2.0/Windows.UI.UIAutomation.UIAutomationContract.winmd": {}, "ref/netstandard2.0/Windows.UI.ViewManagement.ViewManagementViewScalingContract.winmd": {}, "ref/netstandard2.0/Windows.UI.WebUI.Core.WebUICommandBarContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Xaml.Core.Direct.XamlDirectContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Xaml.Hosting.HostingContract.winmd": {}, "ref/netstandard2.0/Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.winmd": {}, "ref/netstandard2.0/Windows.WinMD": {}}, "build": {"build/Microsoft.Windows.SDK.Contracts.props": {}, "build/Microsoft.Windows.SDK.Contracts.targets": {}}}, "Newtonsoft.Json/12.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NSec.Cryptography/20.2.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3", "System.Runtime.CompilerServices.Unsafe": "4.7.0", "libsodium": "[1.0.18, 1.0.19)"}, "compile": {"lib/netstandard2.0/NSec.Cryptography.dll": {}}, "runtime": {"lib/netstandard2.0/NSec.Cryptography.dll": {}}}, "PeterO.Cbor/4.1.3": {"type": "package", "dependencies": {"PeterO.Numbers": "1.6.0", "PeterO.URIUtility": "1.0.0"}, "compile": {"lib/net40/CBOR.dll": {"related": ".xml"}}, "runtime": {"lib/net40/CBOR.dll": {"related": ".xml"}}}, "PeterO.Numbers/1.6.0": {"type": "package", "compile": {"lib/net40/Numbers.dll": {"related": ".xml"}}, "runtime": {"lib/net40/Numbers.dll": {"related": ".xml"}}}, "PeterO.URIUtility/1.0.0": {"type": "package", "compile": {"lib/net40/URIUtility.dll": {"related": ".xml"}}, "runtime": {"lib/net40/URIUtility.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/6.6.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.6.0", "Microsoft.IdentityModel.Tokens": "6.6.0"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.Memory/4.5.4": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.7.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices.WindowsRuntime/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime.WindowsRuntime/4.6.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"buildTransitive/net461/System.Runtime.WindowsRuntime.targets": {}}}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"type": "package", "dependencies": {"System.Runtime.WindowsRuntime": "4.6.0"}, "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"build/net461/System.Runtime.WindowsRuntime.UI.Xaml.targets": {}}}, "System.Security.Cryptography.Cng/4.7.0": {"type": "package", "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"ref/net47/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"lib/net47/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}}, ".NETFramework,Version=v4.8.1/win": {"Fido2/2.0.2": {"type": "package", "dependencies": {"Fido2.Models": "2.0.2", "NSec.Cryptography": "20.2.0", "Newtonsoft.Json": "12.0.3", "PeterO.Cbor": "4.1.3", "System.IdentityModel.Tokens.Jwt": "6.6.0", "System.Memory": "4.5.4", "System.Security.Cryptography.Cng": "4.7.0"}, "compile": {"lib/netstandard2.0/Fido2.dll": {"related": ".pdb"}}, "runtime": {"lib/netstandard2.0/Fido2.dll": {"related": ".pdb"}}, "build": {"build/fido2.targets": {}}}, "Fido2.Models/2.0.2": {"type": "package", "dependencies": {"Newtonsoft.Json": "12.0.3"}, "compile": {"lib/netstandard2.0/Fido2.Models.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Fido2.Models.dll": {"related": ".pdb;.xml"}}}, "libsodium/1.0.18": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1"}}, "Microsoft.IdentityModel.JsonWebTokens/6.6.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "6.6.0"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/6.6.0": {"type": "package", "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/6.6.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "6.6.0"}, "frameworkAssemblies": ["Microsoft.CSharp", "System", "System.Core"], "compile": {"lib/net461/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net461/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.NETCore.Platforms/1.0.1": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Windows.SDK.Contracts/10.0.22621.2428": {"type": "package", "dependencies": {"System.Runtime.InteropServices.WindowsRuntime": "4.3.0", "System.Runtime.WindowsRuntime": "4.6.0", "System.Runtime.WindowsRuntime.UI.Xaml": "4.6.0"}, "compile": {"ref/netstandard2.0/Windows.AI.MachineLearning.MachineLearningContract.winmd": {}, "ref/netstandard2.0/Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivatedEventsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.ContactActivatedEventsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsPhoneContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsVoipContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Calls.LockScreenCallContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.FullTrustAppContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Search.Core.SearchCoreContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Search.SearchContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.SocialInfo.SocialInfoContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.StartupTaskContract.winmd": {}, "ref/netstandard2.0/Windows.ApplicationModel.Wallet.WalletContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Custom.CustomDeviceContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.DevicesLowLevelContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Portable.PortableDeviceContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Printers.Extensions.ExtensionsContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Printers.PrintersContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Scanners.ScannerDeviceContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardEmulatorContract.winmd": {}, "ref/netstandard2.0/Windows.Devices.Sms.LegacySmsApiContract.winmd": {}, "ref/netstandard2.0/Windows.Embedded.DeviceLockdown.DeviceLockdownContract.winmd": {}, "ref/netstandard2.0/Windows.Foundation.FoundationContract.winmd": {}, "ref/netstandard2.0/Windows.Foundation.UniversalApiContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.Input.GamingInputPreviewContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.Preview.GamesEnumerationContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.UI.GameChatOverlayContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.UI.GamingUIProviderContract.winmd": {}, "ref/netstandard2.0/Windows.Gaming.XboxLive.StorageApiContract.winmd": {}, "ref/netstandard2.0/Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.winmd": {}, "ref/netstandard2.0/Windows.Graphics.Printing3D.Printing3DContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Deployment.Preview.DeploymentPreviewContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Deployment.SharedPackageContainerContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Update.WindowsUpdateContract.winmd": {}, "ref/netstandard2.0/Windows.Management.Workplace.WorkplaceSettingsContract.winmd": {}, "ref/netstandard2.0/Windows.Media.AppBroadcasting.AppBroadcastingContract.winmd": {}, "ref/netstandard2.0/Windows.Media.AppRecording.AppRecordingContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.AppBroadcastContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.AppCaptureContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.AppCaptureMetadataContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.CameraCaptureUIContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Capture.GameBarContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Devices.CallControlContract.winmd": {}, "ref/netstandard2.0/Windows.Media.MediaControlContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Playlists.PlaylistsContract.winmd": {}, "ref/netstandard2.0/Windows.Media.Protection.ProtectionRenewalContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.Connectivity.WwanContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.NetworkOperators.NetworkOperatorsFdnContract.WinMD": {}, "ref/netstandard2.0/Windows.Networking.Sockets.ControlChannelTriggerContract.winmd": {}, "ref/netstandard2.0/Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.winmd": {}, "ref/netstandard2.0/Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.winmd": {}, "ref/netstandard2.0/Windows.Phone.PhoneContract.winmd": {}, "ref/netstandard2.0/Windows.Phone.StartScreen.DualSimTileContract.WinMD": {}, "ref/netstandard2.0/Windows.Security.EnterpriseData.EnterpriseDataContract.winmd": {}, "ref/netstandard2.0/Windows.Security.ExchangeActiveSyncProvisioning.EasContract.winmd": {}, "ref/netstandard2.0/Windows.Security.Isolation.Isolatedwindowsenvironmentcontract.winmd": {}, "ref/netstandard2.0/Windows.Services.Maps.GuidanceContract.winmd": {}, "ref/netstandard2.0/Windows.Services.Maps.LocalSearchContract.winmd": {}, "ref/netstandard2.0/Windows.Services.Store.StoreContract.winmd": {}, "ref/netstandard2.0/Windows.Services.TargetedContent.TargetedContentContract.winmd": {}, "ref/netstandard2.0/Windows.Storage.Provider.CloudFilesContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.ProfileHardwareTokenContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.ProfileRetailInfoContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.ProfileSharedModeContract.winmd": {}, "ref/netstandard2.0/Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.winmd": {}, "ref/netstandard2.0/Windows.System.SystemManagementContract.winmd": {}, "ref/netstandard2.0/Windows.System.UserProfile.UserProfileContract.winmd": {}, "ref/netstandard2.0/Windows.System.UserProfile.UserProfileLockScreenContract.winmd": {}, "ref/netstandard2.0/Windows.UI.ApplicationSettings.ApplicationsSettingsContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Core.CoreWindowDialogsContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Shell.SecurityAppManagerContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Shell.WindowTabManagerContract.winmd": {}, "ref/netstandard2.0/Windows.UI.UIAutomation.UIAutomationContract.winmd": {}, "ref/netstandard2.0/Windows.UI.ViewManagement.ViewManagementViewScalingContract.winmd": {}, "ref/netstandard2.0/Windows.UI.WebUI.Core.WebUICommandBarContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Xaml.Core.Direct.XamlDirectContract.winmd": {}, "ref/netstandard2.0/Windows.UI.Xaml.Hosting.HostingContract.winmd": {}, "ref/netstandard2.0/Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.winmd": {}, "ref/netstandard2.0/Windows.WinMD": {}}, "build": {"build/Microsoft.Windows.SDK.Contracts.props": {}, "build/Microsoft.Windows.SDK.Contracts.targets": {}}}, "Newtonsoft.Json/12.0.3": {"type": "package", "compile": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net45/Newtonsoft.Json.dll": {"related": ".xml"}}}, "NSec.Cryptography/20.2.0": {"type": "package", "dependencies": {"System.Memory": "4.5.3", "System.Runtime.CompilerServices.Unsafe": "4.7.0", "libsodium": "[1.0.18, 1.0.19)"}, "compile": {"lib/netstandard2.0/NSec.Cryptography.dll": {}}, "runtime": {"lib/netstandard2.0/NSec.Cryptography.dll": {}}}, "PeterO.Cbor/4.1.3": {"type": "package", "dependencies": {"PeterO.Numbers": "1.6.0", "PeterO.URIUtility": "1.0.0"}, "compile": {"lib/net40/CBOR.dll": {"related": ".xml"}}, "runtime": {"lib/net40/CBOR.dll": {"related": ".xml"}}}, "PeterO.Numbers/1.6.0": {"type": "package", "compile": {"lib/net40/Numbers.dll": {"related": ".xml"}}, "runtime": {"lib/net40/Numbers.dll": {"related": ".xml"}}}, "PeterO.URIUtility/1.0.0": {"type": "package", "compile": {"lib/net40/URIUtility.dll": {"related": ".xml"}}, "runtime": {"lib/net40/URIUtility.dll": {"related": ".xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.IdentityModel.Tokens.Jwt/6.6.0": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.6.0", "Microsoft.IdentityModel.Tokens": "6.6.0"}, "frameworkAssemblies": ["Microsoft.CSharp"], "compile": {"lib/net461/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.Memory/4.5.4": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/4.7.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices.WindowsRuntime/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime.WindowsRuntime/4.6.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"buildTransitive/net461/System.Runtime.WindowsRuntime.targets": {}}}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"type": "package", "dependencies": {"System.Runtime.WindowsRuntime": "4.6.0"}, "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}, "build": {"build/net461/System.Runtime.WindowsRuntime.UI.Xaml.targets": {}}}, "System.Security.Cryptography.Cng/4.7.0": {"type": "package", "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"ref/net47/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll": {"related": ".xml"}}}}}, "libraries": {"Fido2/2.0.2": {"sha512": "IvE4Sv2QgZrzUXkMk3zZqJavO8bSLObSOv01wv2ZfuFw5tpfcOST36AZ5DStkn6bQukECD8ZFAAidkKfKcxzbw==", "type": "package", "path": "fido2/2.0.2", "files": [".nupkg.metadata", ".signature.p7s", "build/fido2.targets", "fido2.2.0.2.nupkg.sha512", "fido2.nuspec", "lib/netcoreapp3.1/Fido2.dll", "lib/netcoreapp3.1/Fido2.pdb", "lib/netstandard2.0/Fido2.dll", "lib/netstandard2.0/Fido2.pdb", "lib/netstandard2.1/Fido2.dll", "lib/netstandard2.1/Fido2.pdb"]}, "Fido2.Models/2.0.2": {"sha512": "8+rS1zUN3/FFAEYVSwHuNQpioSlxPeUKWircpAqQOtlS98g9/ZyLXTrt10Uvw1LWqtMEmrhivoz5ZvjN6d7ZKw==", "type": "package", "path": "fido2.models/2.0.2", "files": [".nupkg.metadata", ".signature.p7s", "fido2.models.2.0.2.nupkg.sha512", "fido2.models.nuspec", "lib/netcoreapp3.1/Fido2.Models.dll", "lib/netcoreapp3.1/Fido2.Models.pdb", "lib/netcoreapp3.1/Fido2.Models.xml", "lib/netstandard2.0/Fido2.Models.dll", "lib/netstandard2.0/Fido2.Models.pdb", "lib/netstandard2.0/Fido2.Models.xml", "lib/netstandard2.1/Fido2.Models.dll", "lib/netstandard2.1/Fido2.Models.pdb", "lib/netstandard2.1/Fido2.Models.xml"]}, "libsodium/1.0.18": {"sha512": "Ajv3AR9Qg/C4SQcE2ONx/UieeKnn5lSvVNc6egC3p6NP6qjZzWJ+Xg2vJURNYjkpHui/KctBwQjMPqpZK8/CHA==", "type": "package", "path": "libsodium/1.0.18", "files": [".nupkg.metadata", ".signature.p7s", "AUTHORS", "ChangeLog", "LICENSE", "libsodium.1.0.18.nupkg.sha512", "libsodium.nuspec", "runtimes/linux-musl-x64/native/libsodium.so", "runtimes/linux-x64/native/libsodium.so", "runtimes/osx-x64/native/libsodium.dylib", "runtimes/win-x64/native/libsodium.dll", "runtimes/win-x86/native/libsodium.dll"]}, "Microsoft.IdentityModel.JsonWebTokens/6.6.0": {"sha512": "ZhzG2+nxbGDJ67vY0e9kuJEtPpA53EhkhXpLWlBOUzEGd085ojJT3XPLK74mC+mjsXpZqWj5pHtgbB77gSqJeg==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/6.6.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net45/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.6.6.0.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/6.6.0": {"sha512": "OWfHX7bXKTEBXcD9b+0Tx7dWBaqF4wht7RmS6q+9vAXHiNpRG/wkoFKhOp1Yus0Xd1xAXXHSkAKxgmn/SRD1KA==", "type": "package", "path": "microsoft.identitymodel.logging/6.6.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Logging.dll", "lib/net45/Microsoft.IdentityModel.Logging.xml", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.6.6.0.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Tokens/6.6.0": {"sha512": "fNfhlY7AEB9y/jooK6xI1vH+7rmq7c2kTEk7j/5SSy25iHRB1twrG8+He9sHxBQqIXhrQliMEwi4GXZInzO4ww==", "type": "package", "path": "microsoft.identitymodel.tokens/6.6.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Microsoft.IdentityModel.Tokens.dll", "lib/net45/Microsoft.IdentityModel.Tokens.xml", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.6.6.0.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.NETCore.Platforms/1.0.1": {"sha512": "2G6OjjJzwBfNOO8myRV/nFrbTw5iA+DEm0N+qUqhrOmaVtn4pC77h38I1jsXGw5VH55+dPfQsqHD0We9sCl9FQ==", "type": "package", "path": "microsoft.netcore.platforms/1.0.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.0.1.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.Windows.SDK.Contracts/10.0.22621.2428": {"sha512": "W+mt71YdJcDtNzgVrRSHQYdj2/p0t7Dlvd0MRVwT57S54uxzsw3Qyv6RjGTUZ6nwOLYe/MA4IMeCHQZqPi3Tpw==", "type": "package", "path": "microsoft.windows.sdk.contracts/10.0.22621.2428", "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.Windows.SDK.Contracts.props", "build/Microsoft.Windows.SDK.Contracts.targets", "c/Catalogs/cat353be8f91891a6a5761b9ac157fa2ff1.cat", "c/Catalogs/cat4ec14c5368b7642563c070cd168960a8.cat", "c/Catalogs/cate59830bab4961666e8d8c2af1e5fa771.cat", "c/Catalogs/catf105a73f98cfc88c7b64d8f7b39a474c.cat", "microsoft.windows.sdk.contracts.10.0.22621.2428.nupkg.sha512", "microsoft.windows.sdk.contracts.nuspec", "ref/netstandard2.0/Windows.AI.MachineLearning.MachineLearningContract.winmd", "ref/netstandard2.0/Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivatedEventsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.ContactActivatedEventsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsPhoneContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.CallsVoipContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Calls.LockScreenCallContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.FullTrustAppContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Search.Core.SearchCoreContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Search.SearchContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.SocialInfo.SocialInfoContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.StartupTaskContract.winmd", "ref/netstandard2.0/Windows.ApplicationModel.Wallet.WalletContract.winmd", "ref/netstandard2.0/Windows.Devices.Custom.CustomDeviceContract.winmd", "ref/netstandard2.0/Windows.Devices.DevicesLowLevelContract.winmd", "ref/netstandard2.0/Windows.Devices.Portable.PortableDeviceContract.winmd", "ref/netstandard2.0/Windows.Devices.Printers.Extensions.ExtensionsContract.winmd", "ref/netstandard2.0/Windows.Devices.Printers.PrintersContract.winmd", "ref/netstandard2.0/Windows.Devices.Scanners.ScannerDeviceContract.winmd", "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.winmd", "ref/netstandard2.0/Windows.Devices.SmartCards.SmartCardEmulatorContract.winmd", "ref/netstandard2.0/Windows.Devices.Sms.LegacySmsApiContract.winmd", "ref/netstandard2.0/Windows.Embedded.DeviceLockdown.DeviceLockdownContract.winmd", "ref/netstandard2.0/Windows.Foundation.FoundationContract.winmd", "ref/netstandard2.0/Windows.Foundation.UniversalApiContract.winmd", "ref/netstandard2.0/Windows.Gaming.Input.GamingInputPreviewContract.winmd", "ref/netstandard2.0/Windows.Gaming.Preview.GamesEnumerationContract.winmd", "ref/netstandard2.0/Windows.Gaming.UI.GameChatOverlayContract.winmd", "ref/netstandard2.0/Windows.Gaming.UI.GamingUIProviderContract.winmd", "ref/netstandard2.0/Windows.Gaming.XboxLive.StorageApiContract.winmd", "ref/netstandard2.0/Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.winmd", "ref/netstandard2.0/Windows.Graphics.Printing3D.Printing3DContract.winmd", "ref/netstandard2.0/Windows.Management.Deployment.Preview.DeploymentPreviewContract.winmd", "ref/netstandard2.0/Windows.Management.Deployment.SharedPackageContainerContract.winmd", "ref/netstandard2.0/Windows.Management.Update.WindowsUpdateContract.winmd", "ref/netstandard2.0/Windows.Management.Workplace.WorkplaceSettingsContract.winmd", "ref/netstandard2.0/Windows.Media.AppBroadcasting.AppBroadcastingContract.winmd", "ref/netstandard2.0/Windows.Media.AppRecording.AppRecordingContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.AppBroadcastContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.AppCaptureContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.AppCaptureMetadataContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.CameraCaptureUIContract.winmd", "ref/netstandard2.0/Windows.Media.Capture.GameBarContract.winmd", "ref/netstandard2.0/Windows.Media.Devices.CallControlContract.winmd", "ref/netstandard2.0/Windows.Media.MediaControlContract.winmd", "ref/netstandard2.0/Windows.Media.Playlists.PlaylistsContract.winmd", "ref/netstandard2.0/Windows.Media.Protection.ProtectionRenewalContract.winmd", "ref/netstandard2.0/Windows.Networking.Connectivity.WwanContract.winmd", "ref/netstandard2.0/Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.winmd", "ref/netstandard2.0/Windows.Networking.NetworkOperators.NetworkOperatorsFdnContract.WinMD", "ref/netstandard2.0/Windows.Networking.Sockets.ControlChannelTriggerContract.winmd", "ref/netstandard2.0/Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.winmd", "ref/netstandard2.0/Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.winmd", "ref/netstandard2.0/Windows.Phone.PhoneContract.winmd", "ref/netstandard2.0/Windows.Phone.StartScreen.DualSimTileContract.WinMD", "ref/netstandard2.0/Windows.Security.EnterpriseData.EnterpriseDataContract.winmd", "ref/netstandard2.0/Windows.Security.ExchangeActiveSyncProvisioning.EasContract.winmd", "ref/netstandard2.0/Windows.Security.Isolation.Isolatedwindowsenvironmentcontract.winmd", "ref/netstandard2.0/Windows.Services.Maps.GuidanceContract.winmd", "ref/netstandard2.0/Windows.Services.Maps.LocalSearchContract.winmd", "ref/netstandard2.0/Windows.Services.Store.StoreContract.winmd", "ref/netstandard2.0/Windows.Services.TargetedContent.TargetedContentContract.winmd", "ref/netstandard2.0/Windows.Storage.Provider.CloudFilesContract.winmd", "ref/netstandard2.0/Windows.System.Profile.ProfileHardwareTokenContract.winmd", "ref/netstandard2.0/Windows.System.Profile.ProfileRetailInfoContract.winmd", "ref/netstandard2.0/Windows.System.Profile.ProfileSharedModeContract.winmd", "ref/netstandard2.0/Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.winmd", "ref/netstandard2.0/Windows.System.SystemManagementContract.winmd", "ref/netstandard2.0/Windows.System.UserProfile.UserProfileContract.winmd", "ref/netstandard2.0/Windows.System.UserProfile.UserProfileLockScreenContract.winmd", "ref/netstandard2.0/Windows.UI.ApplicationSettings.ApplicationsSettingsContract.winmd", "ref/netstandard2.0/Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.winmd", "ref/netstandard2.0/Windows.UI.Core.CoreWindowDialogsContract.winmd", "ref/netstandard2.0/Windows.UI.Shell.SecurityAppManagerContract.winmd", "ref/netstandard2.0/Windows.UI.Shell.WindowTabManagerContract.winmd", "ref/netstandard2.0/Windows.UI.UIAutomation.UIAutomationContract.winmd", "ref/netstandard2.0/Windows.UI.ViewManagement.ViewManagementViewScalingContract.winmd", "ref/netstandard2.0/Windows.UI.WebUI.Core.WebUICommandBarContract.winmd", "ref/netstandard2.0/Windows.UI.Xaml.Core.Direct.XamlDirectContract.winmd", "ref/netstandard2.0/Windows.UI.Xaml.Hosting.HostingContract.winmd", "ref/netstandard2.0/Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.winmd", "ref/netstandard2.0/Windows.WinMD", "ref/netstandard2.0/en/Windows.AI.MachineLearning.MachineLearningContract.xml", "ref/netstandard2.0/en/Windows.AI.MachineLearning.Preview.MachineLearningPreviewContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.ActivatedEventsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.ActivationCameraSettingsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.ContactActivatedEventsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Activation.WebUISearchActivatedEventsContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Background.BackgroundAlarmApplicationContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.Background.CallsBackgroundContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.CallsPhoneContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.CallsVoipContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Calls.LockScreenCallContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.CommunicationBlocking.CommunicationBlockingContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.FullTrustAppContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Preview.InkWorkspace.PreviewInkWorkspaceContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Preview.Notes.PreviewNotesContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Resources.Management.ResourceIndexerContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Search.Core.SearchCoreContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Search.SearchContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.SocialInfo.SocialInfoContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.StartupTaskContract.xml", "ref/netstandard2.0/en/Windows.ApplicationModel.Wallet.WalletContract.xml", "ref/netstandard2.0/en/Windows.Devices.Custom.CustomDeviceContract.xml", "ref/netstandard2.0/en/Windows.Devices.DevicesLowLevelContract.xml", "ref/netstandard2.0/en/Windows.Devices.Portable.PortableDeviceContract.xml", "ref/netstandard2.0/en/Windows.Devices.Printers.Extensions.ExtensionsContract.xml", "ref/netstandard2.0/en/Windows.Devices.Printers.PrintersContract.xml", "ref/netstandard2.0/en/Windows.Devices.Scanners.ScannerDeviceContract.xml", "ref/netstandard2.0/en/Windows.Devices.SmartCards.SmartCardBackgroundTriggerContract.xml", "ref/netstandard2.0/en/Windows.Devices.SmartCards.SmartCardEmulatorContract.xml", "ref/netstandard2.0/en/Windows.Devices.Sms.LegacySmsApiContract.xml", "ref/netstandard2.0/en/Windows.Foundation.FoundationContract.xml", "ref/netstandard2.0/en/Windows.Foundation.UniversalApiContract.xml", "ref/netstandard2.0/en/Windows.Gaming.Input.GamingInputPreviewContract.xml", "ref/netstandard2.0/en/Windows.Gaming.Preview.GamesEnumerationContract.xml", "ref/netstandard2.0/en/Windows.Gaming.UI.GameChatOverlayContract.xml", "ref/netstandard2.0/en/Windows.Gaming.UI.GamingUIProviderContract.xml", "ref/netstandard2.0/en/Windows.Gaming.XboxLive.StorageApiContract.xml", "ref/netstandard2.0/en/Windows.Globalization.GlobalizationJapanesePhoneticAnalyzerContract.xml", "ref/netstandard2.0/en/Windows.Graphics.Printing3D.Printing3DContract.xml", "ref/netstandard2.0/en/Windows.Management.Deployment.Preview.DeploymentPreviewContract.xml", "ref/netstandard2.0/en/Windows.Management.Workplace.WorkplaceSettingsContract.xml", "ref/netstandard2.0/en/Windows.Media.AppBroadcasting.AppBroadcastingContract.xml", "ref/netstandard2.0/en/Windows.Media.AppRecording.AppRecordingContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.AppBroadcastContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.AppCaptureContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.AppCaptureMetadataContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.CameraCaptureUIContract.xml", "ref/netstandard2.0/en/Windows.Media.Capture.GameBarContract.xml", "ref/netstandard2.0/en/Windows.Media.Devices.CallControlContract.xml", "ref/netstandard2.0/en/Windows.Media.MediaControlContract.xml", "ref/netstandard2.0/en/Windows.Media.Playlists.PlaylistsContract.xml", "ref/netstandard2.0/en/Windows.Media.Protection.ProtectionRenewalContract.xml", "ref/netstandard2.0/en/Windows.Networking.Connectivity.WwanContract.xml", "ref/netstandard2.0/en/Windows.Networking.NetworkOperators.LegacyNetworkOperatorsContract.xml", "ref/netstandard2.0/en/Windows.Networking.NetworkOperators.NetworkOperatorsFdnContract.xml", "ref/netstandard2.0/en/Windows.Networking.Sockets.ControlChannelTriggerContract.xml", "ref/netstandard2.0/en/Windows.Networking.XboxLive.XboxLiveSecureSocketsContract.xml", "ref/netstandard2.0/en/Windows.Perception.Automation.Core.PerceptionAutomationCoreContract.xml", "ref/netstandard2.0/en/Windows.Phone.PhoneContract.xml", "ref/netstandard2.0/en/Windows.Phone.StartScreen.DualSimTileContract.xml", "ref/netstandard2.0/en/Windows.Security.EnterpriseData.EnterpriseDataContract.xml", "ref/netstandard2.0/en/Windows.Security.ExchangeActiveSyncProvisioning.EasContract.xml", "ref/netstandard2.0/en/Windows.Security.Isolation.IsolatedWindowsEnvironmentContract.xml", "ref/netstandard2.0/en/Windows.Services.Maps.GuidanceContract.xml", "ref/netstandard2.0/en/Windows.Services.Maps.LocalSearchContract.xml", "ref/netstandard2.0/en/Windows.Services.Store.StoreContract.xml", "ref/netstandard2.0/en/Windows.Services.TargetedContent.TargetedContentContract.xml", "ref/netstandard2.0/en/Windows.Storage.Provider.CloudFilesContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.ProfileHardwareTokenContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.ProfileRetailInfoContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.ProfileSharedModeContract.xml", "ref/netstandard2.0/en/Windows.System.Profile.SystemManufacturers.SystemManufacturersContract.xml", "ref/netstandard2.0/en/Windows.System.SystemManagementContract.xml", "ref/netstandard2.0/en/Windows.System.UserProfile.UserProfileContract.xml", "ref/netstandard2.0/en/Windows.System.UserProfile.UserProfileLockScreenContract.xml", "ref/netstandard2.0/en/Windows.UI.ApplicationSettings.ApplicationsSettingsContract.xml", "ref/netstandard2.0/en/Windows.UI.Core.AnimationMetrics.AnimationMetricsContract.xml", "ref/netstandard2.0/en/Windows.UI.Core.CoreWindowDialogsContract.xml", "ref/netstandard2.0/en/Windows.UI.Shell.SecurityAppManagerContract.xml", "ref/netstandard2.0/en/Windows.UI.UIAutomation.UIAutomationContract.xml", "ref/netstandard2.0/en/Windows.UI.ViewManagement.ViewManagementViewScalingContract.xml", "ref/netstandard2.0/en/Windows.UI.WebUI.Core.WebUICommandBarContract.xml", "ref/netstandard2.0/en/Windows.UI.Xaml.Core.Direct.XamlDirectContract.xml", "ref/netstandard2.0/en/Windows.UI.Xaml.Hosting.HostingContract.xml", "ref/netstandard2.0/en/Windows.Web.Http.Diagnostics.HttpDiagnosticsContract.xml"]}, "Newtonsoft.Json/12.0.3": {"sha512": "6mgjfnRB4jKMlzHSl+VD+oUc1IebOZabkbyWj2RiTgWwYPPuaK1H97G1sHqGwPlS5npiF5Q0OrxN1wni2n5QWg==", "type": "package", "path": "newtonsoft.json/12.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.dll", "lib/portable-net40+sl5+win8+wp8+wpa81/Newtonsoft.Json.xml", "lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.dll", "lib/portable-net45+win8+wp8+wpa81/Newtonsoft.Json.xml", "newtonsoft.json.12.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "NSec.Cryptography/20.2.0": {"sha512": "NxzHaDQm3JfH+9VQdLI1bC4h/ZTKPo5o/4BEscBu4KK0Yv35sB87hSRuzpr09VahxY5ZpJfE2tHyK4u27jfiyQ==", "type": "package", "path": "nsec.cryptography/20.2.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "NOTICE", "lib/netstandard2.0/NSec.Cryptography.dll", "lib/netstandard2.1/NSec.Cryptography.dll", "nsec.cryptography.20.2.0.nupkg.sha512", "nsec.cryptography.nuspec", "nsec.png"]}, "PeterO.Cbor/4.1.3": {"sha512": "+rEfHPsppHqjBTPeDvHUj7KMFrIPbu7TO7QC6s/lFumbFZqJbAUfQkHic2LEW76GX5XNk8aUhwHT/neHwhz2pw==", "type": "package", "path": "petero.cbor/4.1.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net20/CBOR.dll", "lib/net20/CBOR.xml", "lib/net40/CBOR.dll", "lib/net40/CBOR.xml", "lib/netstandard1.0/CBOR.dll", "lib/netstandard1.0/CBOR.xml", "petero.cbor.4.1.3.nupkg.sha512", "petero.cbor.nuspec"]}, "PeterO.Numbers/1.6.0": {"sha512": "sZIzHUwGaK7C2/bDhSIANenpmiIQ8gxG55YCYdLryI8xuQ9lDCRwPN7AzIeJDahi4zFNj7SrKgxhO7gVs21yFw==", "type": "package", "path": "petero.numbers/1.6.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net20/Numbers.dll", "lib/net20/Numbers.xml", "lib/net40/Numbers.dll", "lib/net40/Numbers.xml", "lib/netstandard1.0/Numbers.dll", "lib/netstandard1.0/Numbers.xml", "petero.numbers.1.6.0.nupkg.sha512", "petero.numbers.nuspec"]}, "PeterO.URIUtility/1.0.0": {"sha512": "fpRTBsYACMp7NvTECauYRomubWTC3vUNw4hMXdIedP8ctBGK6tea9HOJwE+qVzis6MZYkL3LIs8qeY3rc6Jdlw==", "type": "package", "path": "petero.uriutility/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net20/URIUtility.dll", "lib/net20/URIUtility.xml", "lib/net40/URIUtility.dll", "lib/net40/URIUtility.xml", "lib/netstandard1.0/URIUtility.dll", "lib/netstandard1.0/URIUtility.xml", "petero.uriutility.1.0.0.nupkg.sha512", "petero.uriutility.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.IdentityModel.Tokens.Jwt/6.6.0": {"sha512": "irSFzRuejokJUb0z/tkHq1x0WCCuk8wtJ85Sql7YaUqSQXPLmKi7zFjVI1UAUX+yANmD673VwN+n64lEq/vW6Q==", "type": "package", "path": "system.identitymodel.tokens.jwt/6.6.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/System.IdentityModel.Tokens.Jwt.dll", "lib/net45/System.IdentityModel.Tokens.Jwt.xml", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.6.6.0.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.Memory/4.5.4": {"sha512": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "type": "package", "path": "system.memory/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.4.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/4.7.0": {"sha512": "IpU1lcHz8/09yDr9N+Juc7SCgNUz+RohkCQI+KsWKR67XxpFr8Z6c8t1iENCXZuRuNCc4HBwme/MDHNVCwyAKg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.7.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.InteropServices.WindowsRuntime/4.3.0": {"sha512": "J4GUi3xZQLUBasNwZnjrffN8i5wpHrBtZoLG+OhRyGo/+YunMRWWtwoMDlUAIdmX0uRfpHIBDSV6zyr3yf00TA==", "type": "package", "path": "system.runtime.interopservices.windowsruntime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.InteropServices.WindowsRuntime.dll", "lib/netstandard1.3/System.Runtime.InteropServices.WindowsRuntime.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios1/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.InteropServices.WindowsRuntime.dll", "ref/netcore50/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/de/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/es/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/fr/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/it/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/ja/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/ko/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/ru/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/System.Runtime.InteropServices.WindowsRuntime.dll", "ref/netstandard1.0/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/de/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/es/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/fr/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/it/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/ja/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/ko/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/ru/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.InteropServices.WindowsRuntime.dll", "system.runtime.interopservices.windowsruntime.4.3.0.nupkg.sha512", "system.runtime.interopservices.windowsruntime.nuspec"]}, "System.Runtime.WindowsRuntime/4.6.0": {"sha512": "IWrs1TmbxP65ZZjIglNyvDkFNoV5q2Pofg5WO7I8RKQOpLdFprQSh3xesOoClBqR4JHr4nEB1Xk1MqLPW1jPuQ==", "type": "package", "path": "system.runtime.windowsruntime/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/net45/System.Runtime.WindowsRuntime.targets", "build/net451/System.Runtime.WindowsRuntime.targets", "build/net461/System.Runtime.WindowsRuntime.targets", "buildTransitive/net45/System.Runtime.WindowsRuntime.targets", "buildTransitive/net451/System.Runtime.WindowsRuntime.targets", "buildTransitive/net461/System.Runtime.WindowsRuntime.targets", "lib/net45/_._", "lib/netstandard1.0/System.Runtime.WindowsRuntime.dll", "lib/netstandard1.0/System.Runtime.WindowsRuntime.xml", "lib/netstandard1.2/System.Runtime.WindowsRuntime.dll", "lib/netstandard1.2/System.Runtime.WindowsRuntime.xml", "lib/netstandard2.0/System.Runtime.WindowsRuntime.dll", "lib/netstandard2.0/System.Runtime.WindowsRuntime.xml", "lib/portable-win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.WindowsRuntime.dll", "ref/netcore50/System.Runtime.WindowsRuntime.xml", "ref/netcore50/de/System.Runtime.WindowsRuntime.xml", "ref/netcore50/es/System.Runtime.WindowsRuntime.xml", "ref/netcore50/fr/System.Runtime.WindowsRuntime.xml", "ref/netcore50/it/System.Runtime.WindowsRuntime.xml", "ref/netcore50/ja/System.Runtime.WindowsRuntime.xml", "ref/netcore50/ko/System.Runtime.WindowsRuntime.xml", "ref/netcore50/ru/System.Runtime.WindowsRuntime.xml", "ref/netcore50/zh-hans/System.Runtime.WindowsRuntime.xml", "ref/netcore50/zh-hant/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/System.Runtime.WindowsRuntime.dll", "ref/netstandard1.0/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/de/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/es/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/fr/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/it/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/ja/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/ko/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/ru/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/System.Runtime.WindowsRuntime.dll", "ref/netstandard1.2/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/de/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/es/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/fr/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/it/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/ja/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/ko/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/ru/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.WindowsRuntime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.WindowsRuntime.xml", "ref/netstandard2.0/System.Runtime.WindowsRuntime.dll", "ref/netstandard2.0/System.Runtime.WindowsRuntime.xml", "ref/portable-win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "runtimes/win-aot/lib/netcore50/System.Runtime.WindowsRuntime.dll", "runtimes/win-aot/lib/uap10.0.16299/_._", "runtimes/win/lib/netcore50/System.Runtime.WindowsRuntime.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.xml", "runtimes/win/lib/uap10.0.16299/_._", "system.runtime.windowsruntime.4.6.0.nupkg.sha512", "system.runtime.windowsruntime.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.WindowsRuntime.UI.Xaml/4.6.0": {"sha512": "r4tNw5v5kqRJ9HikWpcyNf3suGw7DjX93svj9iBjtdeLqL8jt9Z+7f+s4wrKZJr84u8IMsrIjt8K6jYvkRqMSg==", "type": "package", "path": "system.runtime.windowsruntime.ui.xaml/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/net45/System.Runtime.WindowsRuntime.UI.Xaml.targets", "build/net461/System.Runtime.WindowsRuntime.UI.Xaml.targets", "lib/net45/_._", "lib/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.dll", "lib/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.xml", "lib/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.dll", "lib/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.xml", "lib/portable-win8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wpa81/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.WindowsRuntime.UI.Xaml.dll", "ref/netcore50/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/de/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/es/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/fr/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/it/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/ja/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/ko/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/ru/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/zh-hans/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netcore50/zh-hant/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.dll", "ref/netstandard1.1/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/de/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/es/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/fr/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/it/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/ja/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/ko/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/ru/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/zh-hans/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard1.1/zh-hant/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.dll", "ref/netstandard2.0/System.Runtime.WindowsRuntime.UI.Xaml.xml", "ref/portable-win8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wpa81/_._", "runtimes/win-aot/lib/uap10.0.16299/_._", "runtimes/win/lib/netcore50/System.Runtime.WindowsRuntime.UI.Xaml.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.UI.Xaml.dll", "runtimes/win/lib/netcoreapp3.0/System.Runtime.WindowsRuntime.UI.Xaml.xml", "runtimes/win/lib/uap10.0.16299/_._", "system.runtime.windowsruntime.ui.xaml.4.6.0.nupkg.sha512", "system.runtime.windowsruntime.ui.xaml.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Cryptography.Cng/4.7.0": {"sha512": "4WQjFuypWtxb/bl/YwEE7LYGn4fgpsikFfBU6xwEm4YBYiRAhXAEJ62lILBu2JJSFbClIAntFTGfDZafn8yZTg==", "type": "package", "path": "system.security.cryptography.cng/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.xml", "lib/net462/System.Security.Cryptography.Cng.dll", "lib/net462/System.Security.Cryptography.Cng.xml", "lib/net47/System.Security.Cryptography.Cng.dll", "lib/net47/System.Security.Cryptography.Cng.xml", "lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "lib/netstandard1.3/System.Security.Cryptography.Cng.dll", "lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.dll", "lib/netstandard2.0/System.Security.Cryptography.Cng.xml", "lib/netstandard2.1/System.Security.Cryptography.Cng.dll", "lib/netstandard2.1/System.Security.Cryptography.Cng.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.xml", "ref/net462/System.Security.Cryptography.Cng.dll", "ref/net462/System.Security.Cryptography.Cng.xml", "ref/net47/System.Security.Cryptography.Cng.dll", "ref/net47/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.0/System.Security.Cryptography.Cng.xml", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "ref/netcoreapp2.1/System.Security.Cryptography.Cng.xml", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "ref/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.dll", "ref/netstandard2.0/System.Security.Cryptography.Cng.xml", "ref/netstandard2.1/System.Security.Cryptography.Cng.dll", "ref/netstandard2.1/System.Security.Cryptography.Cng.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net462/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net47/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netcoreapp3.0/System.Security.Cryptography.Cng.xml", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.cryptography.cng.4.7.0.nupkg.sha512", "system.security.cryptography.cng.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8.1": ["Fido2 >= 2.0.2", "Microsoft.Windows.SDK.Contracts >= 10.0.22621.2428"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\HelloFIDO\\HelloFIDO.csproj", "projectName": "HelloFIDO", "projectPath": "C:\\Users\\<USER>\\source\\repos\\HelloFIDO\\HelloFIDO.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\HelloFIDO\\obj\\", "projectStyle": "PackageReference", "skipContentFileWrite": true, "UsingMicrosoftNETSdk": false, "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net481"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net481": {"projectReferences": {}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net481": {"dependencies": {"Fido2": {"target": "Package", "version": "[2.0.2, )"}, "Microsoft.Windows.SDK.Contracts": {"target": "Package", "version": "[10.0.22621.2428, )"}}}}, "runtimes": {"win": {"#import": []}}}}