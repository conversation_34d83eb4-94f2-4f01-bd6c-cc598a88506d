using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using KeePassLib.Keys;
using KeePassLib.Utility;

namespace HelloFIDO
{
    /// <summary>
    /// Key provider that implements Windows Hello and FIDO2/WebAuthn authentication
    /// </summary>
    public sealed class HelloFIDOKeyProvider : KeyProvider
    {
        private readonly WindowsHelloAuth _windowsHelloAuth;
        private readonly Fido2Auth _fido2Auth;

        public HelloFIDOKeyProvider()
        {
            _windowsHelloAuth = new WindowsHelloAuth();
            _fido2Auth = new Fido2Auth();
        }

        /// <summary>
        /// Name of the key provider
        /// </summary>
        public override string Name
        {
            get { return "HelloFIDO (Windows Hello + FIDO2)"; }
        }

        /// <summary>
        /// Check if the key provider is available
        /// </summary>
        public bool IsAvailable
        {
            get
            {
                try
                {
                    // Available if either Windows Hello or FIDO2 is available
                    var helloTask = _windowsHelloAuth.IsAvailableAsync();
                    helloTask.Wait(5000); // 5 second timeout

                    return helloTask.Result || _fido2Auth.IsAvailable();
                }
                catch (Exception)
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// Get the key from user authentication
        /// </summary>
        /// <param name="pwDatabase">Database path information</param>
        /// <returns>Composite key or null if authentication failed</returns>
        public override byte[] GetKey(KeyProviderQueryContext ctx)
        {
            if (ctx == null) return null;

            try
            {
                // Show authentication method selection dialog
                var authMethod = ShowAuthenticationMethodDialog();

                switch (authMethod)
                {
                    case AuthenticationMethod.WindowsHello:
                        return GetKeyWithWindowsHello(ctx);

                    case AuthenticationMethod.FIDO2:
                        return GetKeyWithFIDO2(ctx);

                    case AuthenticationMethod.Both:
                        // Try Windows Hello first, then FIDO2 as fallback
                        var helloKey = GetKeyWithWindowsHello(ctx);
                        if (helloKey != null) return helloKey;

                        return GetKeyWithFIDO2(ctx);

                    default:
                        return null;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"HelloFIDO authentication error: {ex.Message}",
                    "HelloFIDO Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        /// <summary>
        /// Get key using Windows Hello authentication
        /// </summary>
        private byte[] GetKeyWithWindowsHello(KeyProviderQueryContext ctx)
        {
            try
            {
                var task = _windowsHelloAuth.AuthenticateAsync("Authenticate with Windows Hello to unlock KeePass database");
                task.Wait();

                if (task.Result)
                {
                    // Generate a deterministic key based on the database path and user
                    return GenerateDeterministicKey(ctx, "WindowsHello");
                }

                return null;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Windows Hello authentication failed: {ex.Message}",
                    "Windows Hello Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        /// <summary>
        /// Get key using FIDO2 authentication
        /// </summary>
        private byte[] GetKeyWithFIDO2(KeyProviderQueryContext ctx)
        {
            try
            {
                // For now, simulate FIDO2 authentication
                // In a real implementation, you would load stored credential data
                byte[] credentialData = LoadFIDO2CredentialData(ctx);

                var task = _fido2Auth.AuthenticateAsync(credentialData);
                task.Wait();

                if (task.Result)
                {
                    // Generate a deterministic key based on the database path and credential
                    return GenerateDeterministicKey(ctx, "FIDO2");
                }

                return null;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"FIDO2 authentication failed: {ex.Message}",
                    "FIDO2 Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        /// <summary>
        /// Generate a deterministic key based on context and method
        /// </summary>
        private byte[] GenerateDeterministicKey(KeyProviderQueryContext ctx, string method)
        {
            try
            {
                // Create a deterministic key based on:
                // - Database path
                // - Authentication method
                // - Machine-specific information

                var keyData = new StringBuilder();
                keyData.Append(ctx.DatabasePath ?? "");
                keyData.Append(method);
                keyData.Append(Environment.MachineName);
                keyData.Append(Environment.UserName);

                // Use SHA-256 to generate a 32-byte key
                using (var sha256 = SHA256.Create())
                {
                    var inputBytes = Encoding.UTF8.GetBytes(keyData.ToString());
                    return sha256.ComputeHash(inputBytes);
                }
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Load FIDO2 credential data for the database
        /// </summary>
        private byte[] LoadFIDO2CredentialData(KeyProviderQueryContext ctx)
        {
            try
            {
                // In a real implementation, you would load this from secure storage
                // For now, return placeholder data
                return Encoding.UTF8.GetBytes("placeholder_fido2_credential");
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Show dialog to select authentication method
        /// </summary>
        private AuthenticationMethod ShowAuthenticationMethodDialog()
        {
            var form = new Form()
            {
                Text = "HelloFIDO Authentication",
                Size = new Size(400, 250),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var label = new Label()
            {
                Text = "Choose authentication method:",
                Location = new Point(20, 20),
                Size = new Size(350, 20)
            };

            var helloButton = new Button()
            {
                Text = "Windows Hello",
                Location = new Point(20, 60),
                Size = new Size(120, 30),
                DialogResult = DialogResult.Yes
            };

            var fido2Button = new Button()
            {
                Text = "FIDO2/WebAuthn",
                Location = new Point(150, 60),
                Size = new Size(120, 30),
                DialogResult = DialogResult.No
            };

            var bothButton = new Button()
            {
                Text = "Try Both",
                Location = new Point(280, 60),
                Size = new Size(80, 30),
                DialogResult = DialogResult.Retry
            };

            var cancelButton = new Button()
            {
                Text = "Cancel",
                Location = new Point(300, 150),
                Size = new Size(80, 30),
                DialogResult = DialogResult.Cancel
            };

            form.Controls.AddRange(new Control[] { label, helloButton, fido2Button, bothButton, cancelButton });

            var result = form.ShowDialog();
            form.Dispose();

            switch (result)
            {
                case DialogResult.Yes:
                    return AuthenticationMethod.WindowsHello;
                case DialogResult.No:
                    return AuthenticationMethod.FIDO2;
                case DialogResult.Retry:
                    return AuthenticationMethod.Both;
                default:
                    return AuthenticationMethod.None;
            }
        }

        /// <summary>
        /// Authentication method enumeration
        /// </summary>
        private enum AuthenticationMethod
        {
            None,
            WindowsHello,
            FIDO2,
            Both
        }
    }
}
