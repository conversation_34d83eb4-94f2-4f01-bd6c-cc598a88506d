@echo off
echo Copying dependencies to KeePass plugins folder...

copy "bin\Debug\Microsoft.IdentityModel.JsonWebTokens.dll" "KeePass-Portable\Plugins\" >nul 2>nul
copy "bin\Debug\Microsoft.IdentityModel.Logging.dll" "KeePass-Portable\Plugins\" >nul 2>nul
copy "bin\Debug\Microsoft.IdentityModel.Tokens.dll" "KeePass-Portable\Plugins\" >nul 2>nul
copy "bin\Debug\System.IdentityModel.Tokens.Jwt.dll" "KeePass-Portable\Plugins\" >nul 2>nul
copy "bin\Debug\System.Memory.dll" "KeePass-Portable\Plugins\" >nul 2>nul
copy "bin\Debug\System.Buffers.dll" "KeePass-Portable\Plugins\" >nul 2>nul
copy "bin\Debug\System.Runtime.CompilerServices.Unsafe.dll" "KeePass-Portable\Plugins\" >nul 2>nul
copy "bin\Debug\System.Numerics.Vectors.dll" "KeePass-Portable\Plugins\" >nul 2>nul
copy "bin\Debug\System.Security.Cryptography.Cng.dll" "KeePass-Portable\Plugins\" >nul 2>nul
copy "bin\Debug\Numbers.dll" "KeePass-Portable\Plugins\" >nul 2>nul
copy "bin\Debug\URIUtility.dll" "KeePass-Portable\Plugins\" >nul 2>nul

echo Dependencies copied.
