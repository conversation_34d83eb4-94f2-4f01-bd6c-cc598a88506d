@echo off
echo Building HelloFIDO KeePass Plugin...
echo.

REM Check if MSBuild is available
where msbuild >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo MSBuild not found in PATH. Trying to locate it...
    
    REM Try common Visual Studio locations
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD=C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD=C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    ) else (
        echo MSBuild not found. Please install Visual Studio or Build Tools.
        pause
        exit /b 1
    )
) else (
    set "MSBUILD=msbuild"
)

echo Using MSBuild: %MSBUILD%
echo.

REM Clean previous builds
echo Cleaning previous builds...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

REM Restore NuGet packages
echo Restoring NuGet packages...
nuget restore HelloFIDO.sln 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo NuGet CLI not found. Trying with dotnet...
    dotnet restore HelloFIDO.sln
    if %ERRORLEVEL% NEQ 0 (
        echo Package restore failed. Please install NuGet CLI or .NET SDK.
        pause
        exit /b 1
    )
    echo Package restore completed with dotnet.
) else (
    echo Package restore completed with NuGet CLI.
)

REM Build Debug version
echo.
echo Building Debug version...
"%MSBUILD%" HelloFIDO.sln /p:Configuration=Debug /p:Platform="Any CPU" /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo Debug build failed!
    pause
    exit /b 1
)

REM Build Release version
echo.
echo Building Release version...
"%MSBUILD%" HelloFIDO.sln /p:Configuration=Release /p:Platform="Any CPU" /v:minimal
if %ERRORLEVEL% NEQ 0 (
    echo Release build failed!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo Output files:
echo - Debug: bin\Debug\HelloFIDO.dll
echo - Release: bin\Release\HelloFIDO.dll
echo.

REM Copy to KeePass plugins folder if it exists
if exist "KeePass-Portable\Plugins" (
    echo Copying plugin and dependencies to KeePass-Portable\Plugins...
    copy "bin\Debug\HelloFIDO.dll" "KeePass-Portable\Plugins\" >nul
    copy "bin\Debug\Fido2.dll" "KeePass-Portable\Plugins\" >nul
    copy "bin\Debug\Fido2.Models.dll" "KeePass-Portable\Plugins\" >nul
    copy "bin\Debug\CBOR.dll" "KeePass-Portable\Plugins\" >nul
    copy "bin\Debug\NSec.Cryptography.dll" "KeePass-Portable\Plugins\" >nul
    copy "bin\Debug\Newtonsoft.Json.dll" "KeePass-Portable\Plugins\" >nul
    copy "bin\Debug\Microsoft.IdentityModel.*.dll" "KeePass-Portable\Plugins\" >nul 2>nul
    copy "bin\Debug\System.IdentityModel.Tokens.Jwt.dll" "KeePass-Portable\Plugins\" >nul
    copy "bin\Debug\System.Memory.dll" "KeePass-Portable\Plugins\" >nul
    copy "bin\Debug\System.Buffers.dll" "KeePass-Portable\Plugins\" >nul
    copy "bin\Debug\System.Runtime.CompilerServices.Unsafe.dll" "KeePass-Portable\Plugins\" >nul
    copy "bin\Debug\System.Numerics.Vectors.dll" "KeePass-Portable\Plugins\" >nul
    copy "bin\Debug\System.Security.Cryptography.Cng.dll" "KeePass-Portable\Plugins\" >nul
    copy "bin\Debug\Numbers.dll" "KeePass-Portable\Plugins\" >nul
    copy "bin\Debug\URIUtility.dll" "KeePass-Portable\Plugins\" >nul
    echo Plugin and dependencies copied for testing.
    echo.
)

echo Build process completed.
pause
